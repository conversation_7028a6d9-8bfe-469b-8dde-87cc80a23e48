import { Router } from "express";
import { OrderController } from "../controllers/orderController";
import { authenticate, authorize } from "../middleware/auth";
import { validate } from "../middleware/validation";
import {
  createOrderSchema,
  createPaymentSchema,
  orderParamsSchema,
  orderQuerySchema,
  paymentParamsSchema,
  updateOrderSchema,
  updatePaymentSchema,
} from "../schemas/order";
import { UserRole } from "../types/firestore";

const router = Router();

// All order routes require authentication
router.use(authenticate);

// Get all orders (All authenticated users)
router.get("/", validate(orderQuerySchema), OrderController.getOrders);

// Create order (All authenticated users)
router.post("/", validate(createOrderSchema), OrderController.createOrder);

// Get order by ID (All authenticated users)
router.get("/:id", validate(orderParamsSchema), OrderController.getOrderById);

// Update order (All authenticated users)
router.put(
  "/:id",
  validate(orderParamsSchema),
  validate(updateOrderSchema),
  OrderController.updateOrder
);

// Payment routes
// Create payment for order (All authenticated users)
router.post(
  "/:orderId/payments",
  validate(createPaymentSchema),
  OrderController.createPayment
);

// Update payment (ADMIN and MANAGER only)
router.put(
  "/:orderId/payments/:paymentId",
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  validate(paymentParamsSchema),
  validate(updatePaymentSchema),
  OrderController.updatePayment
);

export default router;
