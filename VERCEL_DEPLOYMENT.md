# Vercel Deployment Guide for POS Backend

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **Vercel CLI**: Install globally with `npm i -g vercel`
3. **Git Repository**: Your code should be in a Git repository (GitHub, GitLab, or Bitbucket)

## Environment Variables Setup

Before deploying, you need to set up environment variables in Vercel. You can do this via:

### Option 1: Vercel Dashboard

1. Go to your project in Vercel Dashboard
2. Navigate to Settings → Environment Variables
3. Add the following variables:

### Option 2: Vercel CLI

```bash
vercel env add GOOGLE_CLOUD_PROJECT_ID
vercel env add FIRESTORE_PROJECT_ID
vercel env add FIRESTORE_PRIVATE_KEY
vercel env add FIRESTORE_CLIENT_EMAIL
vercel env add JWT_SECRET
vercel env add JWT_EXPIRES_IN
```

### Required Environment Variables

| Variable                  | Value                                                                                | Description                          |
| ------------------------- | ------------------------------------------------------------------------------------ | ------------------------------------ |
| `GOOGLE_CLOUD_PROJECT_ID` | `cash-e4596`                                                                         | Google Cloud Project ID              |
| `FIRESTORE_PROJECT_ID`    | `cash-e4596`                                                                         | Firestore Project ID                 |
| `FIRESTORE_PRIVATE_KEY`   | `-----BEGIN PRIVATE KEY-----\n...`                                                   | Firebase service account private key |
| `FIRESTORE_CLIENT_EMAIL`  | `<EMAIL>`                         | Firebase service account email       |
| `JWT_SECRET`              | `your-super-secret-jwt-key-change-this-in-production-minimum-32-characters-required` | JWT signing secret                   |
| `JWT_EXPIRES_IN`          | `24h`                                                                                | JWT expiration time                  |

**Important**: For `FIRESTORE_PRIVATE_KEY`, make sure to include the full private key with `\n` for line breaks.

## Deployment Steps

### Method 1: Quick Deploy (Recommended)

1. **Login to Vercel**:

   ```bash
   vercel login
   ```

2. **Run the deployment script**:

   ```bash
   npm run deploy
   ```

   This script will:

   - Check if Vercel CLI is installed
   - Verify all required files exist
   - Deploy to production
   - Provide troubleshooting tips if needed

### Method 2: Manual Deploy via Vercel CLI

1. **Login to Vercel**:

   ```bash
   vercel login
   ```

2. **Deploy from project root**:

   ```bash
   vercel
   ```

3. **Follow the prompts**:

   - Link to existing project or create new one
   - Choose settings (usually defaults are fine)

4. **Deploy to production**:

   ```bash
   vercel --prod
   # OR
   npm run deploy:vercel
   ```

### Method 2: Deploy via Git Integration

1. **Connect Repository**:

   - Go to Vercel Dashboard
   - Click "New Project"
   - Import your Git repository

2. **Configure Build Settings**:

   - Framework Preset: Other
   - Build Command: `npm run vercel-build`
   - Output Directory: `dist`
   - Install Command: `npm install`

3. **Add Environment Variables** (as described above)

4. **Deploy**: Click "Deploy"

## Post-Deployment

### 1. Test the Deployment

Test your API endpoints:

```bash
# Health check
curl https://your-app.vercel.app/health

# Test API endpoints
curl https://your-app.vercel.app/api/categories
```

### 2. Update CORS Settings

If you have a frontend, update the CORS configuration in `src/server.ts`:

```typescript
app.use(
  cors({
    origin:
      process.env.NODE_ENV === "production"
        ? ["https://your-frontend-domain.vercel.app"] // Replace with actual domain
        : ["http://localhost:3000", "http://localhost:3001"],
    credentials: true,
  })
);
```

### 3. Custom Domain (Optional)

1. Go to Project Settings → Domains
2. Add your custom domain
3. Configure DNS records as instructed

## Troubleshooting

### Common Issues

1. **Build Failures**:

   - Check build logs in Vercel Dashboard
   - Ensure all dependencies are in `package.json`
   - Verify TypeScript compilation works locally

2. **Environment Variable Issues**:

   - Verify all required env vars are set
   - Check for typos in variable names
   - Ensure private key format is correct

3. **Function Timeout**:

   - Current max duration is set to 30 seconds
   - Optimize slow database queries
   - Consider upgrading Vercel plan for longer timeouts

4. **CORS Errors**:
   - Update allowed origins in `src/server.ts`
   - Ensure credentials are properly configured

### Logs and Monitoring

- **Function Logs**: Available in Vercel Dashboard → Functions tab
- **Real-time Logs**: Use `vercel logs` command
- **Analytics**: Available in Vercel Dashboard

## Security Considerations

1. **Environment Variables**: Never commit sensitive data to Git
2. **CORS**: Restrict origins to your actual domains
3. **Rate Limiting**: Already configured in the application
4. **HTTPS**: Automatically provided by Vercel

## Scaling

- Vercel automatically scales based on demand
- Consider database connection pooling for high traffic
- Monitor function execution time and memory usage

## Support

- Vercel Documentation: [vercel.com/docs](https://vercel.com/docs)
- Vercel Community: [github.com/vercel/vercel/discussions](https://github.com/vercel/vercel/discussions)
