#!/usr/bin/env tsx

/**
 * Firestore Database Seeding Script
 * 
 * This script seeds the Firestore database with initial data for the POS system.
 * It creates sample users, categories, products, customers, and orders.
 * 
 * Usage:
 *   npm run firestore:seed
 *   or
 *   npx tsx scripts/seed-firestore.ts
 */

import bcrypt from 'bcryptjs';
import { firestoreService } from '../src/services/firestoreService';
import { COLLECTIONS } from '../src/types/firestore';
import type { User, Category, Product, Customer, Order } from '../src/types/firestore';
import { logger } from '../src/config/logger';

interface SeedData {
  users: User[];
  categories: Category[];
  products: Product[];
  customers: Customer[];
  orders: Order[];
}

class FirestoreSeeder {
  private seedData: SeedData = {
    users: [],
    categories: [],
    products: [],
    customers: [],
    orders: []
  };

  async seed(): Promise<void> {
    console.log('🌱 Starting Firestore database seeding...\n');

    try {
      // Clear existing data (optional - comment out for production)
      // await this.clearExistingData();

      // Seed data in order (due to dependencies)
      await this.seedUsers();
      await this.seedCategories();
      await this.seedProducts();
      await this.seedCustomers();
      await this.seedOrders();

      console.log('\n🎉 Database seeding completed successfully!');
      this.printSummary();

    } catch (error) {
      console.error('💥 Database seeding failed:', error);
      throw error;
    }
  }

  private async seedUsers(): Promise<void> {
    console.log('👥 Seeding users...');

    const users = [
      {
        email: '<EMAIL>',
        username: 'admin',
        password: await bcrypt.hash('admin123', 12),
        firstName: 'System',
        lastName: 'Administrator',
        role: 'ADMIN' as const,
        isActive: true
      },
      {
        email: '<EMAIL>',
        username: 'manager',
        password: await bcrypt.hash('manager123', 12),
        firstName: 'Store',
        lastName: 'Manager',
        role: 'MANAGER' as const,
        isActive: true
      },
      {
        email: '<EMAIL>',
        username: 'cashier',
        password: await bcrypt.hash('cashier123', 12),
        firstName: 'Store',
        lastName: 'Cashier',
        role: 'CASHIER' as const,
        isActive: true
      }
    ];

    for (const userData of users) {
      const user = await firestoreService.create<User>(COLLECTIONS.USERS, userData);
      this.seedData.users.push(user);
      console.log(`  ✅ Created user: ${user.email} (${user.role})`);
    }
  }

  private async seedCategories(): Promise<void> {
    console.log('\n📂 Seeding categories...');

    const categories = [
      {
        name: 'Beverages',
        description: 'Hot and cold drinks',
        isActive: true
      },
      {
        name: 'Food',
        description: 'Main dishes and snacks',
        isActive: true
      },
      {
        name: 'Desserts',
        description: 'Sweet treats and confectionery',
        isActive: true
      },
      {
        name: 'Pastries',
        description: 'Baked goods and pastries',
        isActive: true
      }
    ];

    for (const categoryData of categories) {
      const category = await firestoreService.create<Category>(COLLECTIONS.CATEGORIES, categoryData);
      this.seedData.categories.push(category);
      console.log(`  ✅ Created category: ${category.name}`);
    }
  }

  private async seedProducts(): Promise<void> {
    console.log('\n🛍️ Seeding products...');

    const beverageCategory = this.seedData.categories.find(c => c.name === 'Beverages');
    const foodCategory = this.seedData.categories.find(c => c.name === 'Food');
    const dessertCategory = this.seedData.categories.find(c => c.name === 'Desserts');
    const pastryCategory = this.seedData.categories.find(c => c.name === 'Pastries');

    const products = [
      // Beverages
      {
        name: 'Espresso',
        description: 'Strong Italian coffee',
        price: 3.50,
        cost: 1.20,
        sku: 'BEV-ESP-001',
        type: 'BEVERAGE' as const,
        categoryId: beverageCategory?.id,
        isActive: true,
        stockQuantity: 100,
        minStockLevel: 20,
        trackInventory: true
      },
      {
        name: 'Cappuccino',
        description: 'Coffee with steamed milk foam',
        price: 4.25,
        cost: 1.50,
        sku: 'BEV-CAP-001',
        type: 'BEVERAGE' as const,
        categoryId: beverageCategory?.id,
        isActive: true,
        stockQuantity: 100,
        minStockLevel: 20,
        trackInventory: true
      },
      {
        name: 'Green Tea',
        description: 'Organic green tea',
        price: 2.75,
        cost: 0.80,
        sku: 'BEV-TEA-001',
        type: 'BEVERAGE' as const,
        categoryId: beverageCategory?.id,
        isActive: true,
        stockQuantity: 150,
        minStockLevel: 30,
        trackInventory: true
      },
      // Food
      {
        name: 'Chicken Sandwich',
        description: 'Grilled chicken with vegetables',
        price: 8.99,
        cost: 4.50,
        sku: 'FOOD-CHK-001',
        type: 'FOOD' as const,
        categoryId: foodCategory?.id,
        isActive: true,
        stockQuantity: 50,
        minStockLevel: 10,
        trackInventory: true
      },
      {
        name: 'Caesar Salad',
        description: 'Fresh romaine lettuce with Caesar dressing',
        price: 7.50,
        cost: 3.25,
        sku: 'FOOD-SAL-001',
        type: 'FOOD' as const,
        categoryId: foodCategory?.id,
        isActive: true,
        stockQuantity: 40,
        minStockLevel: 8,
        trackInventory: true
      },
      // Desserts
      {
        name: 'Chocolate Cake',
        description: 'Rich chocolate layer cake',
        price: 5.99,
        cost: 2.50,
        sku: 'DES-CAK-001',
        type: 'FOOD' as const,
        categoryId: dessertCategory?.id,
        isActive: true,
        stockQuantity: 25,
        minStockLevel: 5,
        trackInventory: true
      },
      {
        name: 'Tiramisu',
        description: 'Italian coffee-flavored dessert',
        price: 6.50,
        cost: 3.00,
        sku: 'DES-TIR-001',
        type: 'FOOD' as const,
        categoryId: dessertCategory?.id,
        isActive: true,
        stockQuantity: 20,
        minStockLevel: 4,
        trackInventory: true
      },
      // Pastries
      {
        name: 'Croissant',
        description: 'Buttery French pastry',
        price: 3.25,
        cost: 1.10,
        sku: 'PAS-CRO-001',
        type: 'FOOD' as const,
        categoryId: pastryCategory?.id,
        isActive: true,
        stockQuantity: 60,
        minStockLevel: 15,
        trackInventory: true
      },
      {
        name: 'Danish Pastry',
        description: 'Sweet pastry with fruit filling',
        price: 3.75,
        cost: 1.30,
        sku: 'PAS-DAN-001',
        type: 'FOOD' as const,
        categoryId: pastryCategory?.id,
        isActive: true,
        stockQuantity: 45,
        minStockLevel: 10,
        trackInventory: true
      }
    ];

    for (const productData of products) {
      const product = await firestoreService.create<Product>(COLLECTIONS.PRODUCTS, productData);
      this.seedData.products.push(product);
      console.log(`  ✅ Created product: ${product.name} - $${product.price}`);
    }
  }

  private async seedCustomers(): Promise<void> {
    console.log('\n👤 Seeding customers...');

    const customers = [
      {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        isActive: true,
        totalOrders: 0,
        totalSpent: 0
      },
      {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+1234567891',
        isActive: true,
        totalOrders: 0,
        totalSpent: 0
      },
      {
        firstName: 'Bob',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '+1234567892',
        isActive: true,
        totalOrders: 0,
        totalSpent: 0
      }
    ];

    for (const customerData of customers) {
      const customer = await firestoreService.create<Customer>(COLLECTIONS.CUSTOMERS, customerData);
      this.seedData.customers.push(customer);
      console.log(`  ✅ Created customer: ${customer.firstName} ${customer.lastName}`);
    }
  }

  private async seedOrders(): Promise<void> {
    console.log('\n🛒 Seeding sample orders...');

    const cashier = this.seedData.users.find(u => u.role === 'CASHIER');
    const customer = this.seedData.customers[0];
    const espresso = this.seedData.products.find(p => p.name === 'Espresso');
    const croissant = this.seedData.products.find(p => p.name === 'Croissant');

    if (!cashier || !customer || !espresso || !croissant) {
      console.log('  ⚠️ Skipping orders - missing required data');
      return;
    }

    const orderData = {
      orderNumber: `ORD-${Date.now()}`,
      status: 'COMPLETED' as const,
      subtotal: 6.75,
      taxAmount: 0.68,
      discountAmount: 0,
      totalAmount: 7.43,
      customerId: customer.id,
      userId: cashier.id,
      orderDate: new Date(),
      completedAt: new Date(),
      orderItems: [
        {
          id: `item-${Date.now()}-1`,
          productId: espresso.id,
          productName: espresso.name,
          quantity: 1,
          unitPrice: espresso.price,
          totalPrice: espresso.price,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: `item-${Date.now()}-2`,
          productId: croissant.id,
          productName: croissant.name,
          quantity: 1,
          unitPrice: croissant.price,
          totalPrice: croissant.price,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ],
      payments: [
        {
          id: `pay-${Date.now()}`,
          amount: 7.43,
          method: 'CASH' as const,
          status: 'COMPLETED' as const,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]
    };

    const order = await firestoreService.create<Order>(COLLECTIONS.ORDERS, orderData);
    this.seedData.orders.push(order);
    console.log(`  ✅ Created order: ${order.orderNumber} - $${order.totalAmount}`);
  }

  private printSummary(): void {
    console.log('\n📊 Seeding Summary:');
    console.log(`  👥 Users: ${this.seedData.users.length}`);
    console.log(`  📂 Categories: ${this.seedData.categories.length}`);
    console.log(`  🛍️ Products: ${this.seedData.products.length}`);
    console.log(`  👤 Customers: ${this.seedData.customers.length}`);
    console.log(`  🛒 Orders: ${this.seedData.orders.length}`);
    
    console.log('\n🔑 Default Login Credentials:');
    console.log('  Admin: <EMAIL> / admin123');
    console.log('  Manager: <EMAIL> / manager123');
    console.log('  Cashier: <EMAIL> / cashier123');
  }
}

// Main execution
async function main() {
  const seeder = new FirestoreSeeder();
  
  try {
    await seeder.seed();
    process.exit(0);
  } catch (error) {
    console.error('💥 Seeding failed:', error);
    process.exit(1);
  }
}

// Run seeding if this script is executed directly
if (require.main === module) {
  main().catch(console.error);
}

export { FirestoreSeeder };
