import { logger } from "../config/logger";
import { CustomError } from "../middleware/errorHandler";
import {
  CreateCustomerInput,
  CustomerQuery,
  UpdateCustomerInput,
} from "../schemas/customer";

export class CustomerService {
  static async createCustomer(data: CreateCustomerInput) {
    try {
      // Check for email uniqueness if provided
      if (data.email) {
        // Import Firestore service and types
        const { firestoreService } = await import("./firestoreService");
        const { COLLECTIONS } = await import("../types/firestore");
        type Customer = import("../types/firestore").Customer;

        const existingCustomers = await firestoreService.findMany<Customer>(
          COLLECTIONS.CUSTOMERS,
          {
            where: [{ field: "email", operator: "==", value: data.email }],
            limit: 1,
          }
        );

        if (existingCustomers.length > 0) {
          throw new CustomError("Customer with this email already exists", 409);
        }
      }

      // Check for phone uniqueness if provided
      if (data.phone) {
        const { firestoreService } = await import("./firestoreService");
        const { COLLECTIONS } = await import("../types/firestore");
        type Customer = import("../types/firestore").Customer;

        const existingCustomers = await firestoreService.findMany<Customer>(
          COLLECTIONS.CUSTOMERS,
          {
            where: [{ field: "phone", operator: "==", value: data.phone }],
            limit: 1,
          }
        );

        if (existingCustomers.length > 0) {
          throw new CustomError(
            "Customer with this phone number already exists",
            409
          );
        }
      }

      // Create customer data with default values
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Customer = import("../types/firestore").Customer;

      const customerData: any = {
        ...data,
        isActive: true,
        totalOrders: 0,
        totalSpent: 0,
      };

      const customer = await firestoreService.create<Customer>(
        COLLECTIONS.CUSTOMERS,
        customerData
      );

      logger.info(
        `Customer created: ${customer.firstName} ${customer.lastName} (ID: ${customer.id})`
      );
      return customer;
    } catch (error) {
      logger.error("Customer creation failed:", error);
      throw error;
    }
  }

  static async getCustomers(query: CustomerQuery) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Customer = import("../types/firestore").Customer;

      const { page, limit, search, isActive } = query;

      // Build where conditions for Firestore
      const whereConditions: Array<{
        field: string;
        operator: any;
        value: any;
      }> = [];

      // Filter by active status
      if (isActive !== undefined) {
        whereConditions.push({
          field: "isActive",
          operator: "==",
          value: isActive,
        });
      }

      let customers: Customer[];
      let total: number;

      // Handle search functionality
      if (search) {
        // Simple search on firstName (Firestore limitation)
        const searchResults = await firestoreService.search<Customer>(
          COLLECTIONS.CUSTOMERS,
          "firstName",
          search.toLowerCase(),
          {
            limit: 1000,
            additionalWhere: whereConditions,
          }
        );

        // Filter results manually for better search
        const filteredCustomers = searchResults.filter((customer: Customer) => {
          const searchLower = search.toLowerCase();
          return (
            customer.firstName.toLowerCase().includes(searchLower) ||
            customer.lastName.toLowerCase().includes(searchLower) ||
            (customer.email &&
              customer.email.toLowerCase().includes(searchLower)) ||
            (customer.phone &&
              customer.phone.toLowerCase().includes(searchLower))
          );
        });

        total = filteredCustomers.length;
        const skip = (page - 1) * limit;
        customers = filteredCustomers.slice(skip, skip + limit);
      } else {
        // Use pagination for regular queries
        const result = await firestoreService.findManyWithPagination<Customer>(
          COLLECTIONS.CUSTOMERS,
          {
            page,
            limit,
            where: whereConditions,
            orderBy: { field: "createdAt", direction: "desc" },
          }
        );

        customers = result.data;
        total = result.pagination.total;
      }

      return {
        customers,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error("Failed to fetch customers:", error);
      throw error;
    }
  }

  static async getCustomerById(id: string) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Customer = import("../types/firestore").Customer;

      const customer = await firestoreService.findById<Customer>(
        COLLECTIONS.CUSTOMERS,
        id
      );

      if (!customer) {
        throw new CustomError("Customer not found", 404);
      }

      // Note: In Firestore, we don't have joins like Prisma
      // Customer orders would need to be fetched separately if needed
      // For now, we return the customer with embedded totalOrders count

      return customer;
    } catch (error) {
      logger.error("Failed to fetch customer:", error);
      throw error;
    }
  }

  static async updateCustomer(id: string, data: UpdateCustomerInput) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Customer = import("../types/firestore").Customer;

      const existingCustomer = await firestoreService.findById<Customer>(
        COLLECTIONS.CUSTOMERS,
        id
      );

      if (!existingCustomer) {
        throw new CustomError("Customer not found", 404);
      }

      // Check for email conflicts if being updated
      if (data.email) {
        const conflictCustomers = await firestoreService.findMany<Customer>(
          COLLECTIONS.CUSTOMERS,
          {
            where: [{ field: "email", operator: "==", value: data.email }],
            limit: 1,
          }
        );

        if (conflictCustomers.length > 0 && conflictCustomers[0].id !== id) {
          throw new CustomError("Customer with this email already exists", 409);
        }
      }

      // Check for phone conflicts if being updated
      if (data.phone) {
        const conflictCustomers = await firestoreService.findMany<Customer>(
          COLLECTIONS.CUSTOMERS,
          {
            where: [{ field: "phone", operator: "==", value: data.phone }],
            limit: 1,
          }
        );

        if (conflictCustomers.length > 0 && conflictCustomers[0].id !== id) {
          throw new CustomError(
            "Customer with this phone number already exists",
            409
          );
        }
      }

      const customer = await firestoreService.update<Customer>(
        COLLECTIONS.CUSTOMERS,
        id,
        data as any
      );

      logger.info(
        `Customer updated: ${customer.firstName} ${customer.lastName} (ID: ${customer.id})`
      );
      return customer;
    } catch (error) {
      logger.error("Customer update failed:", error);
      throw error;
    }
  }

  static async deleteCustomer(id: string) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Customer = import("../types/firestore").Customer;
      type Order = import("../types/firestore").Order;

      const existingCustomer = await firestoreService.findById<Customer>(
        COLLECTIONS.CUSTOMERS,
        id
      );

      if (!existingCustomer) {
        throw new CustomError("Customer not found", 404);
      }

      // Check if customer has orders
      const customerOrders = await firestoreService.findMany<Order>(
        COLLECTIONS.ORDERS,
        {
          where: [{ field: "customerId", operator: "==", value: id }],
          limit: 1,
        }
      );

      if (customerOrders.length > 0) {
        throw new CustomError(
          "Cannot delete customer with existing orders. Consider deactivating instead.",
          400
        );
      }

      await firestoreService.delete(COLLECTIONS.CUSTOMERS, id);

      logger.info(
        `Customer deleted: ${existingCustomer.firstName} ${existingCustomer.lastName} (ID: ${id})`
      );
      return { message: "Customer deleted successfully" };
    } catch (error) {
      logger.error("Customer deletion failed:", error);
      throw error;
    }
  }
}
