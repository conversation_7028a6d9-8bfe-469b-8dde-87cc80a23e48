#!/usr/bin/env tsx

/**
 * Comprehensive Test Runner
 * 
 * This script runs all validation tests for the Firestore-migrated POS backend:
 * - API endpoint tests
 * - Firestore-specific validation
 * - Security feature tests
 * - Performance benchmarks
 * 
 * Usage:
 *   npm run test:all
 *   or
 *   npx tsx tests/run-all-tests.ts
 */

import { APITester } from './api-tests';
import { FirestoreValidator } from './firestore-validation';
import { TEST_CONFIG } from './setup';

interface TestSuiteResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  duration: number;
  details?: any;
  error?: string;
}

class ComprehensiveTestRunner {
  private results: TestSuiteResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting Comprehensive POS Backend Testing Suite');
    console.log('=' .repeat(60));
    console.log(`📍 Target: ${TEST_CONFIG.baseURL}`);
    console.log(`🕐 Started: ${new Date().toISOString()}`);
    console.log('=' .repeat(60));
    console.log();

    try {
      // Run test suites in sequence
      await this.runTestSuite('Server Health Check', this.checkServerHealth.bind(this));
      await this.runTestSuite('API Endpoint Tests', this.runAPITests.bind(this));
      await this.runTestSuite('Firestore Validation', this.runFirestoreValidation.bind(this));
      await this.runTestSuite('Performance Benchmarks', this.runPerformanceBenchmarks.bind(this));
      
      this.printFinalSummary();
      
    } catch (error) {
      console.error('💥 Test suite execution failed:', error);
      throw error;
    }
  }

  private async runTestSuite(name: string, testFn: () => Promise<any>): Promise<void> {
    console.log(`\n🧪 Running ${name}...`);
    console.log('-'.repeat(40));
    
    const startTime = Date.now();
    
    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        status: 'PASS',
        duration,
        details: result
      });
      
      console.log(`✅ ${name} completed successfully (${duration}ms)`);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        status: 'FAIL',
        duration,
        error: error.message
      });
      
      console.log(`❌ ${name} failed (${duration}ms): ${error.message}`);
    }
  }

  private async checkServerHealth(): Promise<any> {
    const axios = (await import('axios')).default;
    
    try {
      const response = await axios.get(`${TEST_CONFIG.baseURL}/health`, {
        timeout: 5000
      });
      
      if (response.status !== 200) {
        throw new Error(`Server health check failed: ${response.status}`);
      }
      
      const data = response.data;
      if (!data.status || data.status !== 'OK') {
        throw new Error('Server health status is not OK');
      }
      
      return {
        status: data.status,
        uptime: data.uptime,
        timestamp: data.timestamp
      };
      
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        throw new Error('Server is not running or not accessible');
      }
      throw error;
    }
  }

  private async runAPITests(): Promise<any> {
    const apiTester = new APITester();
    
    try {
      await apiTester.runAllTests();
      return { message: 'All API tests completed' };
    } catch (error) {
      throw new Error(`API tests failed: ${error.message}`);
    }
  }

  private async runFirestoreValidation(): Promise<any> {
    const firestoreValidator = new FirestoreValidator();
    
    try {
      await firestoreValidator.runValidation();
      return { message: 'All Firestore validation tests completed' };
    } catch (error) {
      throw new Error(`Firestore validation failed: ${error.message}`);
    }
  }

  private async runPerformanceBenchmarks(): Promise<any> {
    console.log('  📊 Running performance benchmarks...');
    
    const axios = (await import('axios')).default;
    const client = axios.create({
      baseURL: TEST_CONFIG.baseURL,
      timeout: 30000
    });

    const benchmarks = [];

    // Health endpoint benchmark
    const healthStart = Date.now();
    for (let i = 0; i < 10; i++) {
      await client.get('/health');
    }
    const healthDuration = Date.now() - healthStart;
    benchmarks.push({
      test: 'Health endpoint (10 requests)',
      duration: healthDuration,
      avgPerRequest: healthDuration / 10
    });

    // Concurrent requests benchmark
    const concurrentStart = Date.now();
    const concurrentPromises = Array(5).fill(0).map(() => client.get('/health'));
    await Promise.all(concurrentPromises);
    const concurrentDuration = Date.now() - concurrentStart;
    benchmarks.push({
      test: 'Concurrent requests (5 parallel)',
      duration: concurrentDuration,
      avgPerRequest: concurrentDuration / 5
    });

    return {
      benchmarks,
      summary: {
        totalTests: benchmarks.length,
        avgResponseTime: benchmarks.reduce((sum, b) => sum + b.avgPerRequest, 0) / benchmarks.length
      }
    };
  }

  private printFinalSummary(): void {
    console.log('\n' + '='.repeat(60));
    console.log('📊 COMPREHENSIVE TEST RESULTS SUMMARY');
    console.log('='.repeat(60));

    const totalSuites = this.results.length;
    const passedSuites = this.results.filter(r => r.status === 'PASS').length;
    const failedSuites = this.results.filter(r => r.status === 'FAIL').length;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
    const passRate = totalSuites > 0 ? (passedSuites / totalSuites * 100).toFixed(1) : '0.0';

    console.log(`\n📈 Overall Results:`);
    console.log(`   Test Suites: ${totalSuites}`);
    console.log(`   Passed: ${passedSuites}`);
    console.log(`   Failed: ${failedSuites}`);
    console.log(`   Pass Rate: ${passRate}%`);
    console.log(`   Total Duration: ${(totalDuration / 1000).toFixed(2)}s`);

    console.log(`\n📋 Suite Details:`);
    this.results.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : '❌';
      const duration = (result.duration / 1000).toFixed(2);
      console.log(`   ${status} ${result.name} (${duration}s)`);
      
      if (result.status === 'FAIL' && result.error) {
        console.log(`      Error: ${result.error}`);
      }
    });

    if (failedSuites === 0) {
      console.log('\n🎉 ALL TESTS PASSED!');
      console.log('✅ The Firestore migration is successful and fully functional');
      console.log('✅ All API endpoints are working correctly');
      console.log('✅ Firestore integration is properly implemented');
      console.log('✅ Security features are functioning as expected');
      console.log('✅ Performance benchmarks are within acceptable ranges');
      
      console.log('\n🚀 The POS backend system is ready for production deployment!');
    } else {
      console.log(`\n⚠️  ${failedSuites} test suite(s) failed.`);
      console.log('❌ Please review and fix the issues before deployment.');
      
      console.log('\n🔧 Recommended Actions:');
      this.results.filter(r => r.status === 'FAIL').forEach(result => {
        console.log(`   • Fix issues in: ${result.name}`);
      });
    }

    console.log('\n' + '='.repeat(60));
    console.log(`🕐 Completed: ${new Date().toISOString()}`);
    console.log('='.repeat(60));
  }
}

// Main execution
async function main() {
  const runner = new ComprehensiveTestRunner();
  
  try {
    await runner.runAllTests();
    
    // Exit with appropriate code
    process.exit(0);
    
  } catch (error) {
    console.error('\n💥 Test execution failed:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Ensure the server is running: npm run dev');
    console.log('   2. Check environment configuration');
    console.log('   3. Verify Firestore credentials');
    console.log('   4. Review server logs for errors');
    
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  main().catch(console.error);
}

export { ComprehensiveTestRunner };
