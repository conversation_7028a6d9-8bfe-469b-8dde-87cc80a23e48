rules_version = '2';

/**
 * Firestore Security Rules for POS Backend System
 * 
 * These rules provide database-level security for the POS system.
 * They work in conjunction with application-level authentication and authorization.
 * 
 * Security Principles:
 * 1. Deny by default - All access is denied unless explicitly allowed
 * 2. Authentication required - All operations require valid authentication
 * 3. Role-based access control - Different roles have different permissions
 * 4. Data validation - Ensure data integrity and prevent malicious input
 * 5. Audit logging - Track all security-relevant operations
 */

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions for authentication and authorization
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserRole() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role;
    }
    
    function isAdmin() {
      return isAuthenticated() && getUserRole() == 'ADMIN';
    }
    
    function isManager() {
      return isAuthenticated() && getUserRole() == 'MANAGER';
    }
    
    function isCashier() {
      return isAuthenticated() && getUserRole() == 'CASHIER';
    }
    
    function isAdminOrManager() {
      return isAdmin() || isManager();
    }
    
    function isValidUser() {
      return isAuthenticated() && 
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isActive == true;
    }
    
    function isOwnerOrAdmin(userId) {
      return isAuthenticated() && (request.auth.uid == userId || isAdmin());
    }
    
    // Data validation functions
    function isValidEmail(email) {
      return email.matches('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$');
    }
    
    function isValidUserRole(role) {
      return role in ['ADMIN', 'MANAGER', 'CASHIER'];
    }
    
    function isValidOrderStatus(status) {
      return status in ['PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'COMPLETED', 'CANCELLED'];
    }
    
    function isValidPaymentMethod(method) {
      return method in ['CASH', 'CARD', 'DIGITAL_WALLET', 'BANK_TRANSFER'];
    }
    
    function isValidPaymentStatus(status) {
      return status in ['PENDING', 'COMPLETED', 'FAILED', 'REFUNDED'];
    }
    
    function isValidProductType(type) {
      return type in ['FOOD', 'BEVERAGE', 'RETAIL', 'SERVICE'];
    }
    
    // Users collection rules
    match /users/{userId} {
      // Read: Users can read their own profile, admins can read all
      allow read: if isOwnerOrAdmin(userId) && isValidUser();
      
      // Create: Only admins can create new users
      allow create: if isAdmin() && 
                       isValidUser() &&
                       isValidEmail(resource.data.email) &&
                       isValidUserRole(resource.data.role) &&
                       resource.data.keys().hasAll(['email', 'username', 'firstName', 'lastName', 'role', 'isActive']) &&
                       resource.data.isActive is bool &&
                       resource.data.email is string &&
                       resource.data.username is string &&
                       resource.data.firstName is string &&
                       resource.data.lastName is string;
      
      // Update: Users can update their own profile (limited fields), admins can update all
      allow update: if isValidUser() && 
                       (
                         // Users can update their own limited fields
                         (request.auth.uid == userId && 
                          request.resource.data.diff(resource.data).affectedKeys().hasOnly(['firstName', 'lastName', 'password']) &&
                          request.resource.data.role == resource.data.role &&
                          request.resource.data.email == resource.data.email &&
                          request.resource.data.username == resource.data.username) ||
                         // Admins can update all fields
                         (isAdmin() && 
                          isValidUserRole(request.resource.data.role) &&
                          isValidEmail(request.resource.data.email))
                       );
      
      // Delete: Only admins can delete users
      allow delete: if isAdmin() && isValidUser();
    }
    
    // Categories collection rules
    match /categories/{categoryId} {
      // Read: All authenticated users can read categories
      allow read: if isValidUser();
      
      // Create/Update/Delete: Only admins and managers
      allow create, update: if isAdminOrManager() && 
                               isValidUser() &&
                               resource.data.keys().hasAll(['name', 'isActive']) &&
                               resource.data.name is string &&
                               resource.data.isActive is bool &&
                               resource.data.name.size() > 0 &&
                               resource.data.name.size() <= 100;
      
      allow delete: if isAdminOrManager() && isValidUser();
    }
    
    // Products collection rules
    match /products/{productId} {
      // Read: All authenticated users can read products
      allow read: if isValidUser();
      
      // Create/Update: Admins and managers can create/update products
      allow create, update: if isAdminOrManager() && 
                               isValidUser() &&
                               resource.data.keys().hasAll(['name', 'price', 'type', 'isActive', 'stockQuantity', 'minStockLevel', 'trackInventory']) &&
                               resource.data.name is string &&
                               resource.data.price is number &&
                               resource.data.price >= 0 &&
                               isValidProductType(resource.data.type) &&
                               resource.data.isActive is bool &&
                               resource.data.stockQuantity is int &&
                               resource.data.stockQuantity >= 0 &&
                               resource.data.minStockLevel is int &&
                               resource.data.minStockLevel >= 0 &&
                               resource.data.trackInventory is bool &&
                               resource.data.name.size() > 0 &&
                               resource.data.name.size() <= 200;
      
      // Delete: Only admins can delete products
      allow delete: if isAdmin() && isValidUser();
    }
    
    // Customers collection rules
    match /customers/{customerId} {
      // Read: All authenticated users can read customers
      allow read: if isValidUser();
      
      // Create/Update: All authenticated users can manage customers
      allow create, update: if isValidUser() &&
                               resource.data.isActive is bool &&
                               (resource.data.email == null || isValidEmail(resource.data.email)) &&
                               resource.data.totalOrders is int &&
                               resource.data.totalOrders >= 0 &&
                               resource.data.totalSpent is number &&
                               resource.data.totalSpent >= 0;
      
      // Delete: Only admins and managers can delete customers
      allow delete: if isAdminOrManager() && isValidUser();
    }
    
    // Orders collection rules
    match /orders/{orderId} {
      // Read: All authenticated users can read orders
      allow read: if isValidUser();
      
      // Create: All authenticated users can create orders
      allow create: if isValidUser() &&
                       resource.data.keys().hasAll(['orderNumber', 'status', 'subtotal', 'taxAmount', 'discountAmount', 'totalAmount', 'userId', 'orderDate']) &&
                       resource.data.orderNumber is string &&
                       isValidOrderStatus(resource.data.status) &&
                       resource.data.subtotal is number &&
                       resource.data.subtotal >= 0 &&
                       resource.data.taxAmount is number &&
                       resource.data.taxAmount >= 0 &&
                       resource.data.discountAmount is number &&
                       resource.data.discountAmount >= 0 &&
                       resource.data.totalAmount is number &&
                       resource.data.totalAmount >= 0 &&
                       resource.data.userId == request.auth.uid &&
                       resource.data.orderDate is timestamp &&
                       // Validate orderItems array
                       resource.data.orderItems is list &&
                       resource.data.orderItems.size() > 0 &&
                       // Validate payments array
                       resource.data.payments is list;
      
      // Update: Users can update their own orders, admins/managers can update all
      allow update: if isValidUser() &&
                       (
                         // Users can update their own orders (limited fields)
                         (resource.data.userId == request.auth.uid &&
                          request.resource.data.diff(resource.data).affectedKeys().hasOnly(['status', 'completedAt', 'payments']) &&
                          isValidOrderStatus(request.resource.data.status)) ||
                         // Admins and managers can update all orders
                         (isAdminOrManager() &&
                          isValidOrderStatus(request.resource.data.status))
                       );
      
      // Delete: Only admins can delete orders
      allow delete: if isAdmin() && isValidUser();
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
