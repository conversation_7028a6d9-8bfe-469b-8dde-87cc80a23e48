import admin from "firebase-admin";
import { Firestore, getFirestore } from "firebase-admin/firestore";
import serviceAccount from "../../cash-e4596.json";
import { logger } from "./logger";

// Firebase Admin configuration
const firebaseConfig = {
  projectId: "cash-e4596",
  // For production, use service account key file or environment variables
  credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
};

// Initialize Firebase Admin SDK
let app: admin.app.App;
let db: Firestore;

try {
  // Check if Firebase app is already initialized
  app = admin.app();
} catch (error) {
  // Initialize Firebase Admin SDK
  if (process.env.NODE_ENV === "production") {
    // In production, use service account credentials
    app = admin.initializeApp({
      credential: admin.credential.applicationDefault(),
      projectId: firebaseConfig.projectId,
    });
  } else {
    // In development, use emulator or default credentials
    app = admin.initializeApp(firebaseConfig);
  }
}

// Get Firestore instance
db = getFirestore(app);

// Configure Firestore settings
db.settings({
  ignoreUndefinedProperties: true,
});

// Log Firestore operations in development
if (process.env.NODE_ENV === "development") {
  logger.info("Firestore initialized for development");
  logger.info(`Project ID: ${firebaseConfig.projectId}`);
}

// Export Firestore instance
export { db as firestore };

// Export admin for other Firebase services if needed
export { admin };

// Graceful shutdown
process.on("beforeExit", async () => {
  try {
    await app.delete();
    logger.info("Firebase connection closed");
  } catch (error) {
    logger.error("Error closing Firebase connection:", error);
  }
});
