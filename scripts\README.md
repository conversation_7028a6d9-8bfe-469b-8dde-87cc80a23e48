# Data Migration Scripts

This directory contains comprehensive data migration scripts for migrating the POS backend system from SQLite (Prisma) to Google Cloud Firestore.

## 📋 Overview

The migration process involves three main scripts:

1. **`migrate-to-firestore.ts`** - Migrates data from SQLite to Firestore
2. **`rollback-from-firestore.ts`** - Rolls back data from Firestore to SQLite
3. **`validate-migration.ts`** - Validates data consistency between databases

## 🚀 Quick Start

### Prerequisites

1. Ensure your SQLite database has data to migrate
2. Configure Firestore credentials in your environment
3. Install dependencies: `npm install`

### Migration Commands

```bash
# Migrate data from SQLite to Firestore
npm run migrate:to-firestore

# Validate migration (compare SQLite vs Firestore)
npm run migrate:validate

# Rollback from Firestore to SQLite (if needed)
npm run migrate:rollback
```

## 📖 Detailed Usage

### 1. Migration to Firestore

```bash
npx tsx scripts/migrate-to-firestore.ts
```

**What it does:**

- Reads all data from SQLite database using Prisma
- Transforms data to match Firestore document structure
- Migrates Users, Categories, Products, Customers, and Orders
- Embeds OrderItems and Payments within Order documents
- Provides progress tracking and error reporting
- Validates migration success

**Features:**

- ✅ Preserves all data relationships
- ✅ Maintains data integrity
- ✅ Handles embedded documents (OrderItems, Payments)
- ✅ Progress tracking with detailed logging
- ✅ Error handling and reporting
- ✅ Automatic validation

### 2. Migration Validation

```bash
npx tsx scripts/validate-migration.ts
```

**What it does:**

- Compares record counts between SQLite and Firestore
- Validates data consistency and integrity
- Checks relationship integrity (embedded documents)
- Provides detailed validation report
- Identifies data discrepancies

**Validation Checks:**

- ✅ Record count comparison
- ✅ Sample data field validation
- ✅ Relationship integrity
- ✅ Embedded document counts
- ✅ Key field consistency

### 3. Rollback to SQLite

```bash
npx tsx scripts/rollback-from-firestore.ts
```

**What it does:**

- Reads data from Firestore collections
- Transforms embedded documents back to relational structure
- Recreates SQLite database with all data
- Validates rollback success

**⚠️ Warning:** This will clear existing SQLite data before rollback!

## 📊 Data Structure Mapping

### SQLite → Firestore Transformation

| SQLite Entity | Firestore Collection | Notes                       |
| ------------- | -------------------- | --------------------------- |
| `users`       | `users`              | Direct mapping              |
| `categories`  | `categories`         | Direct mapping              |
| `products`    | `products`           | Direct mapping              |
| `customers`   | `customers`          | Added analytics fields      |
| `orders`      | `orders`             | Contains embedded documents |
| `order_items` | Embedded in `orders` | As `orderItems` array       |
| `payments`    | Embedded in `orders` | As `payments` array         |

### Key Transformations

1. **Orders Structure:**

   ```typescript
   // SQLite (Relational)
   Order {
     id, orderNumber, status, ...
     orderItems: OrderItem[]  // Separate table
     payments: Payment[]      // Separate table
   }

   // Firestore (Document)
   Order {
     id, orderNumber, status, ...
     orderItems: [            // Embedded array
       { id, productId, quantity, ... }
     ],
     payments: [              // Embedded array
       { id, amount, method, ... }
     ]
   }
   ```

2. **Customer Analytics:**

   ```typescript
   // Added to Firestore Customer documents
   Customer {
     ...existingFields,
     totalOrders: number,     // Calculated field
     totalSpent: number       // Calculated field
   }
   ```

## 🔧 Configuration

### Environment Variables

Ensure these environment variables are set:

```env
# SQLite Database
DATABASE_URL="file:./prisma/dev.db"

# Firestore Configuration
GOOGLE_CLOUD_PROJECT_ID="your-project-id"
GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account.json"
# OR
FIRESTORE_PROJECT_ID="your-project-id"
FIRESTORE_PRIVATE_KEY="your-private-key"
FIRESTORE_CLIENT_EMAIL="your-client-email"
```

### Firestore Setup

1. Create a Google Cloud Project
2. Enable Firestore API
3. Create a service account with Firestore permissions
4. Download service account key or set environment variables

## 📈 Migration Process Flow

```mermaid
graph TD
    A[Start Migration] --> B[Validate Prerequisites]
    B --> C[Migrate Users]
    C --> D[Migrate Categories]
    D --> E[Migrate Products]
    E --> F[Migrate Customers]
    F --> G[Migrate Orders with Embedded Data]
    G --> H[Validate Migration]
    H --> I{Validation Passed?}
    I -->|Yes| J[Migration Complete ✅]
    I -->|No| K[Report Errors ❌]
    K --> L[Manual Review Required]
```

## 🛠️ Troubleshooting

### Common Issues

1. **Firestore Permission Denied**

   ```typescript
   Error: Permission denied
   ```

   - Check service account permissions
   - Verify Firestore API is enabled
   - Ensure correct project ID

2. **SQLite Database Locked**

   ```sql
   Error: database is locked
   ```

   - Stop the development server
   - Close any database connections
   - Retry migration

3. **Memory Issues with Large Datasets**

   ```javascript
   Error: JavaScript heap out of memory
   ```

   - Increase Node.js memory: `node --max-old-space-size=4096`
   - Process data in smaller batches

### Debug Mode

Run scripts with debug logging:

```bash
DEBUG=* npx tsx scripts/migrate-to-firestore.ts
```

## 📋 Migration Checklist

Before migration:

- [ ] Backup SQLite database
- [ ] Configure Firestore credentials
- [ ] Test Firestore connection
- [ ] Stop development server

During migration:

- [ ] Monitor progress logs
- [ ] Check for errors
- [ ] Validate data counts

After migration:

- [ ] Run validation script
- [ ] Test API endpoints
- [ ] Verify data integrity
- [ ] Update application configuration

## 🔄 Rollback Procedure

If migration issues occur:

1. **Immediate Rollback:**

   ```bash
   npm run migrate:rollback
   ```

2. **Restore from Backup:**

   ```bash
   cp prisma/dev.db.backup prisma/dev.db
   ```

3. **Restart Services:**

   ```bash
   npm run dev
   ```

## 📞 Support

For migration issues:

1. Check the error logs in the console output
2. Verify environment configuration
3. Review the troubleshooting section
4. Check Firestore console for data
5. Validate SQLite data integrity

## 🔐 Security Notes

- Service account keys contain sensitive credentials
- Never commit credentials to version control
- Use environment variables for production
- Rotate service account keys regularly
- Follow principle of least privilege for Firestore rules
