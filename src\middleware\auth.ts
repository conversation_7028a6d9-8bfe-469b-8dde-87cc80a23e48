import { NextFunction, Request, Response } from "express";
import jwt from "jsonwebtoken";
import { UserRole } from "../types/firestore";
import { CustomError } from "./errorHandler";

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: UserRole;
    username: string;
  };
}

export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    const clientIP = req.ip || req.connection.remoteAddress;
    const userAgent = req.get("User-Agent");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      // Log failed authentication attempt
      const { logSecurityEvent } = await import("../config/security");
      logSecurityEvent(
        "auth_failed_no_token",
        undefined,
        {
          ip: clientIP,
          userAgent,
          url: req.url,
          method: req.method,
        },
        "warn"
      );

      throw new CustomError("Access token required", 401);
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    if (!process.env.JWT_SECRET) {
      throw new CustomError("JWT secret not configured", 500);
    }

    // Enhanced JWT verification with additional options
    const decoded = jwt.verify(token, process.env.JWT_SECRET, {
      algorithms: ["HS256"],
      issuer: "pos-backend",
      audience: "pos-frontend",
    }) as any;

    // Verify user still exists and is active
    // Import Firestore service and types
    const { firestoreService } = await import("../services/firestoreService");
    const { COLLECTIONS } = await import("../types/firestore");
    type User = import("../types/firestore").User;

    const user = await firestoreService.findById<User>(
      COLLECTIONS.USERS,
      decoded.userId
    );

    if (!user || !user.isActive) {
      // Log failed authentication attempt
      const { logSecurityEvent } = await import("../config/security");
      logSecurityEvent(
        "auth_failed_invalid_user",
        decoded.userId,
        {
          ip: clientIP,
          userAgent,
          reason: !user ? "user_not_found" : "user_inactive",
        },
        "warn"
      );

      throw new CustomError("Invalid or expired token", 401);
    }

    // Log successful authentication
    const { logSecurityEvent } = await import("../config/security");
    logSecurityEvent("auth_success", user.id, {
      ip: clientIP,
      userAgent,
      role: user.role,
    });

    req.user = user;
    next();
  } catch (error) {
    const clientIP = req.ip || req.connection.remoteAddress;
    const userAgent = req.get("User-Agent");

    if (error instanceof jwt.JsonWebTokenError) {
      // Log JWT-specific errors
      const { logSecurityEvent } = await import("../config/security");
      logSecurityEvent(
        "auth_failed_jwt_error",
        undefined,
        {
          ip: clientIP,
          userAgent,
          error: error.message,
          errorType: error.name,
        },
        "warn"
      );

      next(new CustomError("Invalid token", 401));
    } else {
      // Log other authentication errors
      const { logSecurityEvent } = await import("../config/security");
      logSecurityEvent(
        "auth_failed_unknown_error",
        undefined,
        {
          ip: clientIP,
          userAgent,
          error: error.message,
        },
        "error"
      );

      next(error);
    }
  }
};

export const authorize = (...roles: UserRole[]) => {
  return async (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ) => {
    if (!req.user) {
      return next(new CustomError("Authentication required", 401));
    }

    if (!roles.includes(req.user.role)) {
      // Log authorization failure
      const { logSecurityEvent } = await import("../config/security");
      const clientIP = req.ip || req.connection.remoteAddress;
      const userAgent = req.get("User-Agent");

      logSecurityEvent(
        "authorization_failed",
        req.user.id,
        {
          ip: clientIP,
          userAgent,
          userRole: req.user.role,
          requiredRoles: roles,
          url: req.url,
          method: req.method,
        },
        "warn"
      );

      return next(new CustomError("Insufficient permissions", 403));
    }

    // Log successful authorization for sensitive operations
    if (req.method !== "GET") {
      const { logSecurityEvent } = await import("../config/security");
      const clientIP = req.ip || req.connection.remoteAddress;

      logSecurityEvent("authorization_success", req.user.id, {
        ip: clientIP,
        userRole: req.user.role,
        url: req.url,
        method: req.method,
      });
    }

    next();
  };
};
