import { Request, Response } from 'express';
import { UserService } from '../services/userService';
import { asyncHandler } from '../middleware/errorHandler';
import { CreateUserInput, UpdateUserInput, UserParams, UserQuery } from '../schemas/user';

export class UserController {
  static createUser = asyncHandler(async (req: Request, res: Response) => {
    const data: CreateUserInput = req.body;
    const user = await UserService.createUser(data);
    
    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: user
    });
  });

  static getUsers = asyncHandler(async (req: Request, res: Response) => {
    const query: UserQuery = req.query as any;
    const result = await UserService.getUsers(query);
    
    res.status(200).json({
      success: true,
      message: 'Users retrieved successfully',
      data: result.users,
      pagination: result.pagination
    });
  });

  static getUserById = asyncHandler(async (req: Request, res: Response) => {
    const { id }: UserParams = req.params as any;
    const user = await UserService.getUserById(id);
    
    res.status(200).json({
      success: true,
      message: 'User retrieved successfully',
      data: user
    });
  });

  static updateUser = asyncHandler(async (req: Request, res: Response) => {
    const { id }: UserParams = req.params as any;
    const data: UpdateUserInput = req.body;
    const user = await UserService.updateUser(id, data);
    
    res.status(200).json({
      success: true,
      message: 'User updated successfully',
      data: user
    });
  });

  static deleteUser = asyncHandler(async (req: Request, res: Response) => {
    const { id }: UserParams = req.params as any;
    const result = await UserService.deleteUser(id);
    
    res.status(200).json({
      success: true,
      message: result.message
    });
  });
}
