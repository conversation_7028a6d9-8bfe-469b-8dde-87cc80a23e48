import { Request, Response } from 'express';
import { CustomerService } from '../services/customerService';
import { asyncHandler } from '../middleware/errorHandler';
import { 
  CreateCustomerInput, 
  UpdateCustomerInput, 
  CustomerParams, 
  CustomerQuery 
} from '../schemas/customer';

export class CustomerController {
  static createCustomer = asyncHandler(async (req: Request, res: Response) => {
    const data: CreateCustomerInput = req.body;
    const customer = await CustomerService.createCustomer(data);
    
    res.status(201).json({
      success: true,
      message: 'Customer created successfully',
      data: customer
    });
  });

  static getCustomers = asyncHandler(async (req: Request, res: Response) => {
    const query: CustomerQuery = req.query as any;
    const result = await CustomerService.getCustomers(query);
    
    res.status(200).json({
      success: true,
      message: 'Customers retrieved successfully',
      data: result.customers,
      pagination: result.pagination
    });
  });

  static getCustomerById = asyncHandler(async (req: Request, res: Response) => {
    const { id }: CustomerParams = req.params as any;
    const customer = await CustomerService.getCustomerById(id);
    
    res.status(200).json({
      success: true,
      message: 'Customer retrieved successfully',
      data: customer
    });
  });

  static updateCustomer = asyncHandler(async (req: Request, res: Response) => {
    const { id }: CustomerParams = req.params as any;
    const data: UpdateCustomerInput = req.body;
    const customer = await CustomerService.updateCustomer(id, data);
    
    res.status(200).json({
      success: true,
      message: 'Customer updated successfully',
      data: customer
    });
  });

  static deleteCustomer = asyncHandler(async (req: Request, res: Response) => {
    const { id }: CustomerParams = req.params as any;
    const result = await CustomerService.deleteCustomer(id);
    
    res.status(200).json({
      success: true,
      message: result.message,
      data: null
    });
  });
}
