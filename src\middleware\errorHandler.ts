import { NextFunction, Request, Response } from "express";
import { ZodError } from "zod";
import { logger } from "../config/logger";

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export class CustomError extends Error implements AppError {
  statusCode: number;
  isOperational: boolean;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorHandler = (
  error: AppError | ZodError | Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let statusCode = 500;
  let message = "Internal Server Error";
  let details: any = undefined;

  // Log the error
  logger.error("Error occurred:", {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get("User-Agent"),
  });

  // Handle different types of errors
  if (error instanceof CustomError) {
    statusCode = error.statusCode;
    message = error.message;
  } else if (error instanceof ZodError) {
    statusCode = 400;
    message = "Validation Error";
    details = error.errors.map((err) => ({
      field: err.path.join("."),
      message: err.message,
    }));
  } else if (
    error.name === "FirebaseError" ||
    error.message?.includes("Firestore")
  ) {
    // Handle Firestore errors
    statusCode = 500;
    message = "Database operation failed.";

    // Check for specific Firestore error codes
    if (error.message?.includes("NOT_FOUND")) {
      statusCode = 404;
      message = "Document not found.";
    } else if (error.message?.includes("ALREADY_EXISTS")) {
      statusCode = 409;
      message = "Document already exists.";
    } else if (error.message?.includes("PERMISSION_DENIED")) {
      statusCode = 403;
      message = "Permission denied.";
    } else if (error.message?.includes("INVALID_ARGUMENT")) {
      statusCode = 400;
      message = "Invalid data provided.";
    } else if (error.message?.includes("DEADLINE_EXCEEDED")) {
      statusCode = 408;
      message = "Request timeout.";
    }
  }

  // Don't expose internal errors in production
  if (process.env.NODE_ENV === "production" && statusCode === 500) {
    message = "Something went wrong!";
  }

  res.status(statusCode).json({
    success: false,
    message,
    ...(details && { details }),
    ...(process.env.NODE_ENV === "development" && { stack: error.stack }),
  });
};

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
