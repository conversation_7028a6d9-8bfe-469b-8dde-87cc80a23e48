import { Router } from "express";
import { CategoryController } from "../controllers/categoryController";
import { authenticate, authorize } from "../middleware/auth";
import { validate } from "../middleware/validation";
import {
  categoryParamsSchema,
  categoryQuerySchema,
  createCategorySchema,
  updateCategorySchema,
} from "../schemas/category";
import { UserRole } from "../types/firestore";

const router = Router();

// All category routes require authentication
router.use(authenticate);

// Get all categories (All authenticated users)
router.get(
  "/",
  validate(categoryQuerySchema),
  CategoryController.getCategories
);

// Create category (ADMIN and MANAGER only)
router.post(
  "/",
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  validate(createCategorySchema),
  CategoryController.createCategory
);

// Get category by ID (All authenticated users)
router.get(
  "/:id",
  validate(categoryParamsSchema),
  CategoryController.getCategoryById
);

// Update category (ADMIN and MANAGER only)
router.put(
  "/:id",
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  validate(categoryParamsSchema),
  validate(updateCategorySchema),
  CategoryController.updateCategory
);

// Delete category (ADMIN only)
router.delete(
  "/:id",
  authorize(UserRole.ADMIN),
  validate(categoryParamsSchema),
  CategoryController.deleteCategory
);

export default router;
