#!/usr/bin/env node

/**
 * Deployment script for POS Backend to Vercel
 * This script helps automate the deployment process
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting POS Backend deployment to Vercel...\n');

// Check if vercel CLI is installed
try {
  execSync('vercel --version', { stdio: 'pipe' });
  console.log('✅ Vercel CLI is installed');
} catch (error) {
  console.log('❌ Vercel CLI not found. Installing...');
  try {
    execSync('npm install -g vercel', { stdio: 'inherit' });
    console.log('✅ Vercel CLI installed successfully');
  } catch (installError) {
    console.error('❌ Failed to install Vercel CLI:', installError.message);
    process.exit(1);
  }
}

// Check if required files exist
const requiredFiles = [
  'src/server.ts',
  'vercel.json',
  'package.json',
  'tsconfig.json'
];

console.log('\n📋 Checking required files...');
for (const file of requiredFiles) {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} exists`);
  } else {
    console.error(`❌ ${file} is missing`);
    process.exit(1);
  }
}

// Check if service account file exists
if (fs.existsSync('cash-e4596.json')) {
  console.log('✅ Firebase service account file exists');
} else {
  console.log('⚠️  Firebase service account file not found. Make sure to set environment variables in Vercel.');
}

console.log('\n🔧 Environment Variables Required:');
console.log('Make sure these are set in your Vercel project:');
console.log('- GOOGLE_CLOUD_PROJECT_ID');
console.log('- FIRESTORE_PROJECT_ID');
console.log('- FIRESTORE_PRIVATE_KEY');
console.log('- FIRESTORE_CLIENT_EMAIL');
console.log('- JWT_SECRET');
console.log('- JWT_EXPIRES_IN');

console.log('\n🚀 Deploying to Vercel...');

try {
  // Deploy to Vercel
  execSync('vercel --prod', { stdio: 'inherit' });
  console.log('\n✅ Deployment completed successfully!');
  console.log('\n📝 Next steps:');
  console.log('1. Test your API endpoints');
  console.log('2. Update CORS settings if needed');
  console.log('3. Configure custom domain (optional)');
} catch (error) {
  console.error('\n❌ Deployment failed:', error.message);
  console.log('\n🔍 Troubleshooting tips:');
  console.log('1. Check if all environment variables are set');
  console.log('2. Verify your Vercel account has proper permissions');
  console.log('3. Check the build logs in Vercel dashboard');
  process.exit(1);
}
