import { z } from "zod";
import { OrderStatus, PaymentMethod, PaymentStatus } from "../types/firestore";

// Order item schema for creating orders
export const orderItemSchema = z.object({
  productId: z.string().min(1, "Product ID is required"),
  quantity: z.number().int().positive("Quantity must be positive"),
  notes: z.string().optional(),
});

export const createOrderSchema = z.object({
  body: z.object({
    customerId: z.string().min(1, "Customer ID is required").optional(),
    items: z.array(orderItemSchema).min(1, "Order must have at least one item"),
    taxAmount: z.number().min(0, "Tax amount cannot be negative").default(0),
    discountAmount: z
      .number()
      .min(0, "Discount amount cannot be negative")
      .default(0),
    notes: z.string().optional(),
  }),
});

export const updateOrderSchema = z.object({
  body: z.object({
    status: z.nativeEnum(OrderStatus).optional(),
    customerId: z.string().min(1, "Customer ID is required").optional(),
    taxAmount: z.number().min(0, "Tax amount cannot be negative").optional(),
    discountAmount: z
      .number()
      .min(0, "Discount amount cannot be negative")
      .optional(),
    notes: z.string().optional(),
  }),
});

export const orderParamsSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Order ID is required"),
  }),
});

export const orderQuerySchema = z.object({
  query: z.object({
    page: z
      .string()
      .optional()
      .default("1")
      .transform(Number)
      .pipe(z.number().min(1)),
    limit: z
      .string()
      .optional()
      .default("10")
      .transform(Number)
      .pipe(z.number().min(1).max(100)),
    search: z.string().optional(),
    status: z.nativeEnum(OrderStatus).optional(),
    customerId: z.string().min(1, "Customer ID is required").optional(),
    userId: z.string().min(1, "User ID is required").optional(),
    startDate: z.string().datetime("Invalid start date").optional(),
    endDate: z.string().datetime("Invalid end date").optional(),
    minAmount: z.string().transform(Number).pipe(z.number().min(0)).optional(),
    maxAmount: z.string().transform(Number).pipe(z.number().min(0)).optional(),
  }),
});

// Payment schemas
export const createPaymentSchema = z.object({
  body: z.object({
    amount: z.number().positive("Payment amount must be positive"),
    method: z.nativeEnum(PaymentMethod),
    reference: z.string().optional(),
    notes: z.string().optional(),
  }),
});

export const updatePaymentSchema = z.object({
  body: z.object({
    status: z.nativeEnum(PaymentStatus),
    reference: z.string().optional(),
    notes: z.string().optional(),
  }),
});

export const paymentParamsSchema = z.object({
  params: z.object({
    orderId: z.string().min(1, "Order ID is required"),
    paymentId: z.string().min(1, "Payment ID is required"),
  }),
});

export type CreateOrderInput = z.infer<typeof createOrderSchema>["body"];
export type UpdateOrderInput = z.infer<typeof updateOrderSchema>["body"];
export type OrderParams = z.infer<typeof orderParamsSchema>["params"];
export type OrderQuery = z.infer<typeof orderQuerySchema>["query"];
export type OrderItemInput = z.infer<typeof orderItemSchema>;

export type CreatePaymentInput = z.infer<typeof createPaymentSchema>["body"];
export type UpdatePaymentInput = z.infer<typeof updatePaymentSchema>["body"];
export type PaymentParams = z.infer<typeof paymentParamsSchema>["params"];
