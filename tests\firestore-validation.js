#!/usr/bin/env tsx
"use strict";
/**
 * Firestore-Specific Validation Tests
 *
 * This script validates Firestore-specific functionality including:
 * - Document structure validation
 * - Embedded document handling
 * - Query performance
 * - Data consistency
 * - Transaction handling
 *
 * Usage:
 *   npm run test:firestore
 *   or
 *   npx tsx tests/firestore-validation.ts
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.FirestoreValidator = void 0;
const firestoreService_1 = require("../src/services/firestoreService");
const firestore_1 = require("../src/types/firestore");
const setup_1 = require("./setup");
class FirestoreValidator {
    constructor() {
        this.results = [];
    }
    async runValidation() {
        console.log("🔥 Starting Firestore-Specific Validation Tests\n");
        try {
            await this.validateDocumentStructure();
            await this.validateEmbeddedDocuments();
            await this.validateQueryPerformance();
            await this.validateDataConsistency();
            await this.validateTransactionHandling();
            await this.validateSearchFunctionality();
            await this.validatePaginationFeatures();
            this.printResults();
        }
        catch (error) {
            console.error("💥 Firestore validation failed:", error);
            throw error;
        }
        finally {
            await setup_1.TestCleanup.cleanup();
        }
    }
    async validateDocumentStructure() {
        console.log("📄 Validating Document Structure...");
        // Test User document structure
        await this.runTest("User document structure", async () => {
            const testUser = setup_1.TestUtils.generateTestUser();
            const user = await firestoreService_1.firestoreService.create(firestore_1.COLLECTIONS.USERS, testUser);
            // Validate required fields
            const requiredFields = [
                "id",
                "email",
                "username",
                "firstName",
                "lastName",
                "role",
                "isActive",
                "createdAt",
                "updatedAt",
            ];
            for (const field of requiredFields) {
                if (!(field in user)) {
                    throw new Error(`Missing required field: ${field}`);
                }
            }
            // Validate data types
            if (typeof user.email !== "string")
                throw new Error("email must be string");
            if (typeof user.isActive !== "boolean")
                throw new Error("isActive must be boolean");
            if (!(user.createdAt instanceof Date))
                throw new Error("createdAt must be Date");
            setup_1.TestCleanup.trackResource("users", user.id);
            return { userId: user.id, fields: Object.keys(user) };
        });
        // Test Category document structure
        await this.runTest("Category document structure", async () => {
            const testCategory = setup_1.TestUtils.generateTestCategory();
            const category = await firestoreService_1.firestoreService.create(firestore_1.COLLECTIONS.CATEGORIES, testCategory);
            const requiredFields = [
                "id",
                "name",
                "isActive",
                "createdAt",
                "updatedAt",
            ];
            for (const field of requiredFields) {
                if (!(field in category)) {
                    throw new Error(`Missing required field: ${field}`);
                }
            }
            setup_1.TestCleanup.trackResource("categories", category.id);
            return { categoryId: category.id, fields: Object.keys(category) };
        });
        // Test Product document structure
        await this.runTest("Product document structure", async () => {
            const testProduct = setup_1.TestUtils.generateTestProduct();
            const product = await firestoreService_1.firestoreService.create(firestore_1.COLLECTIONS.PRODUCTS, testProduct);
            const requiredFields = [
                "id",
                "name",
                "price",
                "type",
                "isActive",
                "stockQuantity",
                "minStockLevel",
                "trackInventory",
            ];
            for (const field of requiredFields) {
                if (!(field in product)) {
                    throw new Error(`Missing required field: ${field}`);
                }
            }
            if (typeof product.price !== "number")
                throw new Error("price must be number");
            if (typeof product.stockQuantity !== "number")
                throw new Error("stockQuantity must be number");
            setup_1.TestCleanup.trackResource("products", product.id);
            return { productId: product.id, fields: Object.keys(product) };
        });
    }
    async validateEmbeddedDocuments() {
        console.log("📦 Validating Embedded Documents...");
        await this.runTest("Order with embedded OrderItems and Payments", async () => {
            // Create test data
            const testUser = setup_1.TestUtils.generateTestUser();
            const user = await firestoreService_1.firestoreService.create(firestore_1.COLLECTIONS.USERS, testUser);
            const testCustomer = setup_1.TestUtils.generateTestCustomer();
            const customer = await firestoreService_1.firestoreService.create(firestore_1.COLLECTIONS.CUSTOMERS, testCustomer);
            const testProduct = setup_1.TestUtils.generateTestProduct();
            const product = await firestoreService_1.firestoreService.create(firestore_1.COLLECTIONS.PRODUCTS, testProduct);
            // Create order with embedded documents
            const orderData = {
                orderNumber: `TEST-${Date.now()}`,
                status: "PENDING",
                subtotal: 25.98,
                taxAmount: 2.6,
                discountAmount: 0,
                totalAmount: 28.58,
                customerId: customer.id,
                userId: user.id,
                orderDate: new Date(),
                orderItems: [
                    {
                        id: setup_1.TestUtils.generateRandomString(),
                        productId: product.id,
                        productName: product.name,
                        quantity: 2,
                        unitPrice: 12.99,
                        totalPrice: 25.98,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                    },
                ],
                payments: [
                    {
                        id: setup_1.TestUtils.generateRandomString(),
                        amount: 28.58,
                        method: "CASH",
                        status: "COMPLETED",
                        createdAt: new Date(),
                        updatedAt: new Date(),
                    },
                ],
            };
            const order = await firestoreService_1.firestoreService.create(firestore_1.COLLECTIONS.ORDERS, orderData);
            // Validate embedded documents
            if (!order.orderItems || order.orderItems.length === 0) {
                throw new Error("OrderItems not properly embedded");
            }
            if (!order.payments || order.payments.length === 0) {
                throw new Error("Payments not properly embedded");
            }
            const orderItem = order.orderItems[0];
            if (!orderItem.productId ||
                !orderItem.productName ||
                !orderItem.quantity) {
                throw new Error("OrderItem missing required fields");
            }
            const payment = order.payments[0];
            if (!payment.amount || !payment.method || !payment.status) {
                throw new Error("Payment missing required fields");
            }
            setup_1.TestCleanup.trackResource("orders", order.id);
            setup_1.TestCleanup.trackResource("users", user.id);
            setup_1.TestCleanup.trackResource("customers", customer.id);
            setup_1.TestCleanup.trackResource("products", product.id);
            return {
                orderId: order.id,
                orderItemsCount: order.orderItems.length,
                paymentsCount: order.payments.length,
            };
        });
    }
    async validateQueryPerformance() {
        console.log("⚡ Validating Query Performance...");
        await this.runTest("Query performance with filters", async () => {
            const startTime = Date.now();
            // Test filtered query
            const activeProducts = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.PRODUCTS, {
                where: [{ field: "isActive", operator: "==", value: true }],
                limit: 50,
            });
            const queryDuration = Date.now() - startTime;
            if (queryDuration > 5000) {
                // 5 seconds threshold
                throw new Error(`Query too slow: ${queryDuration}ms`);
            }
            return {
                resultsCount: activeProducts.length,
                queryDuration,
                performanceGrade: queryDuration < 1000
                    ? "EXCELLENT"
                    : queryDuration < 3000
                        ? "GOOD"
                        : "ACCEPTABLE",
            };
        });
        await this.runTest("Pagination performance", async () => {
            const startTime = Date.now();
            const result = await firestoreService_1.firestoreService.findManyWithPagination(firestore_1.COLLECTIONS.PRODUCTS, {
                page: 1,
                limit: 10,
                orderBy: { field: "createdAt", direction: "desc" },
            });
            const queryDuration = Date.now() - startTime;
            return {
                resultsCount: result.data.length,
                totalCount: result.pagination.total,
                queryDuration,
                hasNextPage: result.pagination.hasNextPage,
            };
        });
    }
    async validateDataConsistency() {
        console.log("🔄 Validating Data Consistency...");
        await this.runTest("CRUD operations consistency", async () => {
            // Create
            const testCategory = setup_1.TestUtils.generateTestCategory();
            const created = await firestoreService_1.firestoreService.create(firestore_1.COLLECTIONS.CATEGORIES, testCategory);
            // Read
            const read = await firestoreService_1.firestoreService.findById(firestore_1.COLLECTIONS.CATEGORIES, created.id);
            if (!read || read.name !== created.name) {
                throw new Error("Read operation inconsistent with create");
            }
            // Update
            const updateData = { name: "Updated Test Category" };
            const updated = await firestoreService_1.firestoreService.update(firestore_1.COLLECTIONS.CATEGORIES, created.id, updateData);
            if (updated.name !== updateData.name) {
                throw new Error("Update operation failed");
            }
            // Verify update
            const readAfterUpdate = await firestoreService_1.firestoreService.findById(firestore_1.COLLECTIONS.CATEGORIES, created.id);
            if (!readAfterUpdate || readAfterUpdate.name !== updateData.name) {
                throw new Error("Update not persisted correctly");
            }
            // Delete
            await firestoreService_1.firestoreService.delete(firestore_1.COLLECTIONS.CATEGORIES, created.id);
            // Verify deletion
            const readAfterDelete = await firestoreService_1.firestoreService.findById(firestore_1.COLLECTIONS.CATEGORIES, created.id);
            if (readAfterDelete) {
                throw new Error("Delete operation failed");
            }
            return {
                operations: ["CREATE", "READ", "UPDATE", "DELETE"],
                allSuccessful: true,
            };
        });
    }
    async validateTransactionHandling() {
        console.log("💳 Validating Transaction Handling...");
        await this.runTest("Firestore transaction support", async () => {
            // Note: This is a basic test since we don't have explicit transaction methods
            // In a real implementation, you would test actual Firestore transactions
            const testCategory1 = setup_1.TestUtils.generateTestCategory();
            const testCategory2 = setup_1.TestUtils.generateTestCategory();
            // Simulate batch operations
            const category1 = await firestoreService_1.firestoreService.create(firestore_1.COLLECTIONS.CATEGORIES, testCategory1);
            const category2 = await firestoreService_1.firestoreService.create(firestore_1.COLLECTIONS.CATEGORIES, testCategory2);
            // Verify both were created
            const read1 = await firestoreService_1.firestoreService.findById(firestore_1.COLLECTIONS.CATEGORIES, category1.id);
            const read2 = await firestoreService_1.firestoreService.findById(firestore_1.COLLECTIONS.CATEGORIES, category2.id);
            if (!read1 || !read2) {
                throw new Error("Batch operations failed");
            }
            setup_1.TestCleanup.trackResource("categories", category1.id);
            setup_1.TestCleanup.trackResource("categories", category2.id);
            return {
                batchOperations: 2,
                allSuccessful: true,
            };
        });
    }
    async validateSearchFunctionality() {
        console.log("🔍 Validating Search Functionality...");
        await this.runTest("Search functionality", async () => {
            // Create test data with searchable content
            const testProduct = {
                ...setup_1.TestUtils.generateTestProduct(),
                name: "Searchable Test Product",
                description: "This is a searchable test product description",
            };
            const product = await firestoreService_1.firestoreService.create(firestore_1.COLLECTIONS.PRODUCTS, testProduct);
            // Test search
            const searchResults = await firestoreService_1.firestoreService.search(firestore_1.COLLECTIONS.PRODUCTS, "name", "searchable", { limit: 10 });
            const found = searchResults.find((p) => p.id === product.id);
            if (!found) {
                throw new Error("Search functionality failed to find created product");
            }
            setup_1.TestCleanup.trackResource("products", product.id);
            return {
                searchTerm: "searchable",
                resultsCount: searchResults.length,
                foundTargetProduct: !!found,
            };
        });
    }
    async validatePaginationFeatures() {
        console.log("📄 Validating Pagination Features...");
        await this.runTest("Pagination with ordering", async () => {
            // Create multiple test categories
            const categories = [];
            for (let i = 0; i < 5; i++) {
                const testCategory = setup_1.TestUtils.generateTestCategory();
                const category = await firestoreService_1.firestoreService.create(firestore_1.COLLECTIONS.CATEGORIES, testCategory);
                categories.push(category);
                setup_1.TestCleanup.trackResource("categories", category.id);
            }
            // Test pagination
            const page1 = await firestoreService_1.firestoreService.findManyWithPagination(firestore_1.COLLECTIONS.CATEGORIES, {
                page: 1,
                limit: 3,
                orderBy: { field: "createdAt", direction: "desc" },
            });
            if (page1.data.length === 0) {
                throw new Error("Pagination returned no results");
            }
            if (page1.data.length > 3) {
                throw new Error("Pagination limit not respected");
            }
            // Verify ordering
            for (let i = 1; i < page1.data.length; i++) {
                if (page1.data[i].createdAt > page1.data[i - 1].createdAt) {
                    throw new Error("Results not properly ordered by createdAt desc");
                }
            }
            return {
                page: 1,
                limit: 3,
                resultsCount: page1.data.length,
                totalCount: page1.pagination.total,
                hasNextPage: page1.pagination.hasNextPage,
                properlyOrdered: true,
            };
        });
    }
    async runTest(testName, testFn) {
        const startTime = Date.now();
        try {
            console.log(`  🧪 ${testName}...`);
            const result = await testFn();
            const duration = Date.now() - startTime;
            this.results.push({
                test: testName,
                status: "PASS",
                duration,
                details: result,
            });
            console.log(`    ✅ PASS (${duration}ms)`);
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.results.push({
                test: testName,
                status: "FAIL",
                duration,
                error: error.message,
            });
            console.log(`    ❌ FAIL (${duration}ms): ${error.message}`);
        }
    }
    printResults() {
        console.log("\n📊 Firestore Validation Results");
        console.log("=".repeat(50));
        const totalTests = this.results.length;
        const passedTests = this.results.filter((r) => r.status === "PASS").length;
        const failedTests = this.results.filter((r) => r.status === "FAIL").length;
        const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
        const passRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : "0.0";
        console.log(`\n📈 Summary:`);
        console.log(`   Total Tests: ${totalTests}`);
        console.log(`   Passed: ${passedTests}`);
        console.log(`   Failed: ${failedTests}`);
        console.log(`   Pass Rate: ${passRate}%`);
        console.log(`   Total Duration: ${totalDuration}ms`);
        if (failedTests > 0) {
            console.log("\n❌ Failed Tests:");
            this.results
                .filter((r) => r.status === "FAIL")
                .forEach((result) => {
                console.log(`   • ${result.test}: ${result.error}`);
            });
        }
        if (failedTests === 0) {
            console.log("\n🎉 All Firestore validation tests passed!");
            console.log("✅ Document structure is correct");
            console.log("✅ Embedded documents work properly");
            console.log("✅ Query performance is acceptable");
            console.log("✅ Data consistency is maintained");
            console.log("✅ Search functionality works");
            console.log("✅ Pagination features work correctly");
        }
        else {
            console.log(`\n⚠️  ${failedTests} validation test(s) failed.`);
            console.log("Please review the Firestore implementation.");
        }
    }
}
exports.FirestoreValidator = FirestoreValidator;
// Main execution
async function main() {
    const validator = new FirestoreValidator();
    try {
        await validator.runValidation();
    }
    catch (error) {
        console.error("💥 Firestore validation failed:", error);
        process.exit(1);
    }
}
// Run validation if this script is executed directly
if (require.main === module) {
    main().catch(console.error);
}
