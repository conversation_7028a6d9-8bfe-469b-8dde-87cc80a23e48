import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { logger } from "../config/logger";
import { CustomError } from "../middleware/errorHandler";
import { LoginInput, RegisterInput } from "../schemas/auth";

export class AuthService {
  private static readonly SALT_ROUNDS = 12;
  private static readonly JWT_SECRET = process.env.JWT_SECRET!;
  private static readonly JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "24h";

  static async register(data: RegisterInput) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type User = import("../types/firestore").User;

      // Check if user already exists by email
      const existingUserByEmail = await firestoreService.findMany<User>(
        COLLECTIONS.USERS,
        {
          where: [{ field: "email", operator: "==", value: data.email }],
          limit: 1,
        }
      );

      if (existingUserByEmail.length > 0) {
        throw new CustomError("User with this email already exists", 409);
      }

      // Check if user already exists by username
      const existingUserByUsername = await firestoreService.findMany<User>(
        COLLECTIONS.USERS,
        {
          where: [{ field: "username", operator: "==", value: data.username }],
          limit: 1,
        }
      );

      if (existingUserByUsername.length > 0) {
        throw new CustomError("User with this username already exists", 409);
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(data.password, this.SALT_ROUNDS);

      // Create user
      const userData = {
        ...data,
        password: hashedPassword,
        isActive: true,
      };

      const user = await firestoreService.create<User>(
        COLLECTIONS.USERS,
        userData
      );

      logger.info(`New user registered: ${user.email}`);

      // Return user without password
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      logger.error("Registration failed:", error);
      throw error;
    }
  }

  static async login(data: LoginInput) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type User = import("../types/firestore").User;

      // Find user by email
      const users = await firestoreService.findMany<User>(COLLECTIONS.USERS, {
        where: [{ field: "email", operator: "==", value: data.email }],
        limit: 1,
      });

      const user = users[0];
      if (!user || !user.isActive) {
        throw new CustomError("Invalid credentials", 401);
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(
        data.password,
        user.password
      );
      if (!isPasswordValid) {
        throw new CustomError("Invalid credentials", 401);
      }

      // Generate JWT token with enhanced security
      const token = jwt.sign(
        {
          userId: user.id,
          email: user.email,
          role: user.role,
          iat: Math.floor(Date.now() / 1000),
          jti: Math.random().toString(36).substring(2, 15), // Unique token ID
        },
        this.JWT_SECRET,
        {
          expiresIn: this.JWT_EXPIRES_IN,
          algorithm: "HS256",
          issuer: "pos-backend",
          audience: "pos-frontend",
        }
      );

      logger.info(`User logged in: ${user.email}`);

      return {
        token,
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
        },
      };
    } catch (error) {
      logger.error("Login failed:", error);
      throw error;
    }
  }

  static async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type User = import("../types/firestore").User;

      // Get user
      const user = await firestoreService.findById<User>(
        COLLECTIONS.USERS,
        userId
      );

      if (!user) {
        throw new CustomError("User not found", 404);
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(
        currentPassword,
        user.password
      );
      if (!isCurrentPasswordValid) {
        throw new CustomError("Current password is incorrect", 400);
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(
        newPassword,
        this.SALT_ROUNDS
      );

      // Update password
      await firestoreService.update<User>(COLLECTIONS.USERS, userId, {
        password: hashedNewPassword,
      });

      logger.info(`Password changed for user: ${user.email}`);
      return { message: "Password changed successfully" };
    } catch (error) {
      logger.error("Password change failed:", error);
      throw error;
    }
  }

  static async refreshToken(userId: string) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type User = import("../types/firestore").User;

      const user = await firestoreService.findById<User>(
        COLLECTIONS.USERS,
        userId
      );

      if (!user || !user.isActive) {
        throw new CustomError("User not found or inactive", 404);
      }

      const token = jwt.sign(
        {
          userId: user.id,
          email: user.email,
          role: user.role,
          iat: Math.floor(Date.now() / 1000),
          jti: Math.random().toString(36).substring(2, 15), // Unique token ID
        },
        this.JWT_SECRET,
        {
          expiresIn: this.JWT_EXPIRES_IN,
          algorithm: "HS256",
          issuer: "pos-backend",
          audience: "pos-frontend",
        }
      );

      // Return user without password
      const { password, ...userWithoutPassword } = user;
      return { token, user: userWithoutPassword };
    } catch (error) {
      logger.error("Token refresh failed:", error);
      throw error;
    }
  }
}
