# 🔒 SECURITY ENHANCEMENTS - Critical Implementation Guide

## Overview
This document addresses the critical security vulnerabilities identified in the audit and provides specific implementation fixes.

## 1. Enhanced Input Validation

### Problem
Current validation schemas lack comprehensive edge case protection.

### Critical Fixes Needed

**1.1 String Length Limits**
```typescript
// src/schemas/product.ts - ENHANCED VERSION
export const createProductSchema = z.object({
  body: z.object({
    name: z.string()
      .min(1, 'Product name is required')
      .max(100, 'Product name too long')
      .regex(/^[a-zA-Z0-9\s\-_.,()]+$/, 'Invalid characters in product name'),
    
    description: z.string()
      .max(1000, 'Description too long')
      .optional(),
    
    price: z.number()
      .positive('Price must be positive')
      .max(999999.99, 'Price too high')
      .multipleOf(0.01, 'Price must have at most 2 decimal places'),
    
    sku: z.string()
      .min(3, 'SKU too short')
      .max(50, 'SKU too long')
      .regex(/^[A-Z0-9_-]+$/, 'SKU must contain only uppercase letters, numbers, hyphens, and underscores')
      .optional(),
    
    barcode: z.string()
      .min(8, 'Barcode too short')
      .max(20, 'Barcode too long')
      .regex(/^[0-9]+$/, 'Barcode must contain only numbers')
      .optional(),
    
    allergens: z.string()
      .max(500, 'Allergens description too long')
      .optional()
  })
});
```

**1.2 Enhanced Email/Phone Validation**
```typescript
// src/schemas/customer.ts - ENHANCED VERSION
export const createCustomerSchema = z.object({
  body: z.object({
    firstName: z.string()
      .min(1, 'First name required')
      .max(50, 'First name too long')
      .regex(/^[a-zA-Z\s'-]+$/, 'Invalid characters in first name')
      .optional(),
    
    lastName: z.string()
      .min(1, 'Last name required')
      .max(50, 'Last name too long')
      .regex(/^[a-zA-Z\s'-]+$/, 'Invalid characters in last name')
      .optional(),
    
    email: z.string()
      .email('Invalid email format')
      .max(254, 'Email too long')
      .toLowerCase()
      .refine(
        (email) => !email.includes('..') && !email.startsWith('.') && !email.endsWith('.'),
        'Invalid email format'
      )
      .optional(),
    
    phone: z.string()
      .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format')
      .min(10, 'Phone number too short')
      .max(15, 'Phone number too long')
      .optional()
  })
});
```

## 2. Advanced XSS Protection

### Problem
Current regex-based sanitization is insufficient for advanced XSS attacks.

### Solution: Implement DOMPurify and Enhanced CSP

**2.1 Install DOMPurify**
```bash
npm install isomorphic-dompurify
npm install --save-dev @types/dompurify
```

**2.2 Enhanced Sanitization Middleware**
```typescript
// src/middleware/security.ts - ENHANCED VERSION
import DOMPurify from 'isomorphic-dompurify';

export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  const sanitizeObject = <T>(obj: T): T => {
    if (typeof obj === 'string') {
      // Use DOMPurify for comprehensive XSS protection
      const cleaned = DOMPurify.sanitize(obj, {
        ALLOWED_TAGS: [], // No HTML tags allowed
        ALLOWED_ATTR: [],
        KEEP_CONTENT: true,
        RETURN_DOM: false,
        RETURN_DOM_FRAGMENT: false,
        RETURN_DOM_IMPORT: false
      });
      
      // Additional custom sanitization
      return cleaned
        .replace(/[<>]/g, '') // Remove any remaining angle brackets
        .replace(/javascript:/gi, '') // Remove javascript: protocols
        .replace(/on\w+\s*=/gi, '') // Remove event handlers
        .replace(/data:/gi, '') // Remove data: URLs
        .trim() as T;
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject) as T;
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized = {} as T;
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          (sanitized as any)[key] = sanitizeObject((obj as any)[key]);
        }
      }
      return sanitized;
    }
    
    return obj;
  };

  // Sanitize all input
  if (req.body) req.body = sanitizeObject(req.body);
  if (req.query) req.query = sanitizeObject(req.query);
  if (req.params) req.params = sanitizeObject(req.params);

  next();
};
```

**2.3 Enhanced Content Security Policy**
```typescript
// src/middleware/security.ts - ENHANCED CSP
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
      childSrc: ["'none'"],
      workerSrc: ["'none'"],
      manifestSrc: ["'self'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      frameAncestors: ["'none'"],
      upgradeInsecureRequests: []
    },
  },
  crossOriginEmbedderPolicy: { policy: "require-corp" },
  crossOriginOpenerPolicy: { policy: "same-origin" },
  crossOriginResourcePolicy: { policy: "same-origin" },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  },
  noSniff: true,
  xssFilter: true,
  referrerPolicy: { policy: "strict-origin-when-cross-origin" }
});
```

## 3. CSRF Protection Implementation

### Problem
No CSRF protection currently implemented.

### Solution: Add CSRF Tokens

**3.1 Install CSRF Protection**
```bash
npm install csurf
npm install --save-dev @types/csurf
```

**3.2 Implement CSRF Middleware**
```typescript
// src/middleware/csrf.ts - NEW FILE
import csrf from 'csurf';
import { Request, Response, NextFunction } from 'express';

// CSRF protection for state-changing operations
export const csrfProtection = csrf({
  cookie: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 3600000 // 1 hour
  },
  ignoreMethods: ['GET', 'HEAD', 'OPTIONS'],
  value: (req: Request) => {
    return req.body._csrf || 
           req.query._csrf || 
           req.headers['x-csrf-token'] ||
           req.headers['x-xsrf-token'];
  }
});

// Endpoint to get CSRF token
export const getCsrfToken = (req: Request, res: Response) => {
  res.json({
    success: true,
    csrfToken: req.csrfToken()
  });
};
```

**3.3 Apply CSRF Protection**
```typescript
// src/app.ts - ADD CSRF PROTECTION
import { csrfProtection, getCsrfToken } from './middleware/csrf';

// CSRF token endpoint
app.get('/api/csrf-token', csrfProtection, getCsrfToken);

// Apply CSRF protection to state-changing routes
app.use('/api/products', csrfProtection);
app.use('/api/orders', csrfProtection);
app.use('/api/customers', csrfProtection);
app.use('/api/users', csrfProtection);
app.use('/api/categories', csrfProtection);
```

## 4. Enhanced Rate Limiting

### Problem
Current rate limits may be too permissive for production.

### Solution: Implement Adaptive Rate Limiting

```typescript
// src/middleware/security.ts - ENHANCED RATE LIMITING
export const strictAuthLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // Limit to 3 login attempts per 15 minutes
  skipSuccessfulRequests: true,
  message: {
    success: false,
    message: 'Too many login attempts. Account temporarily locked.',
    retryAfter: '15 minutes'
  }
});

export const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // Reduced from 100 to 50 requests per 15 minutes
  message: {
    success: false,
    message: 'API rate limit exceeded. Please slow down.',
    retryAfter: '15 minutes'
  }
});

export const createLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // Reduced from 10 to 5 create operations per minute
  message: {
    success: false,
    message: 'Too many create operations. Please wait.',
    retryAfter: '1 minute'
  }
});

// IP-based progressive rate limiting
export const progressiveRateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: (req: Request) => {
    // More restrictive for unauthenticated users
    return req.user ? 30 : 10;
  },
  keyGenerator: (req: Request) => {
    // Use user ID for authenticated users, IP for others
    return req.user?.id || req.ip;
  }
});
```

## 5. Data Encryption Implementation

### Problem
Sensitive data stored in plain text.

### Solution: Field-Level Encryption

**5.1 Install Encryption Library**
```bash
npm install crypto-js
npm install --save-dev @types/crypto-js
```

**5.2 Encryption Utility**
```typescript
// src/utils/encryption.ts - NEW FILE
import CryptoJS from 'crypto-js';

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';

export class EncryptionUtil {
  static encrypt(text: string): string {
    return CryptoJS.AES.encrypt(text, ENCRYPTION_KEY).toString();
  }

  static decrypt(encryptedText: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedText, ENCRYPTION_KEY);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  static hashSensitiveData(data: string): string {
    return CryptoJS.SHA256(data).toString();
  }
}
```

**5.3 Apply Encryption to Sensitive Fields**
```typescript
// src/services/customerService.ts - ADD ENCRYPTION
import { EncryptionUtil } from '../utils/encryption';

static async createCustomer(data: CreateCustomerInput) {
  try {
    // Encrypt sensitive data before storing
    const encryptedData = {
      ...data,
      email: data.email ? EncryptionUtil.encrypt(data.email) : undefined,
      phone: data.phone ? EncryptionUtil.encrypt(data.phone) : undefined
    };

    const customer = await prisma.customer.create({
      data: encryptedData
    });

    // Decrypt for response
    return {
      ...customer,
      email: customer.email ? EncryptionUtil.decrypt(customer.email) : null,
      phone: customer.phone ? EncryptionUtil.decrypt(customer.phone) : null
    };
  } catch (error) {
    logger.error('Customer creation failed:', error);
    throw error;
  }
}
```

## 6. Implementation Priority

### CRITICAL (Week 1)
1. ✅ Enhanced input validation
2. ✅ Advanced XSS protection
3. ✅ CSRF protection
4. ✅ Stricter rate limiting

### HIGH (Week 2)
1. ✅ Data encryption implementation
2. ✅ Security headers enhancement
3. ✅ Audit logging setup

### MEDIUM (Week 3-4)
1. ✅ Security monitoring
2. ✅ Penetration testing
3. ✅ Security documentation

## 7. Testing Security Enhancements

```bash
# Test XSS protection
curl -X POST http://localhost:3000/api/products \
  -H "Content-Type: application/json" \
  -d '{"name":"<script>alert(\"xss\")</script>Test Product"}'

# Test rate limiting
for i in {1..10}; do curl http://localhost:3000/api/products; done

# Test CSRF protection
curl -X POST http://localhost:3000/api/products \
  -H "Content-Type: application/json" \
  -d '{"name":"Test"}' # Should fail without CSRF token
```

## 8. Security Monitoring

Implement security event logging for:
- Failed authentication attempts
- Rate limit violations
- Input validation failures
- CSRF token mismatches
- Suspicious request patterns

This comprehensive security enhancement will significantly improve the system's resistance to common web application attacks.
