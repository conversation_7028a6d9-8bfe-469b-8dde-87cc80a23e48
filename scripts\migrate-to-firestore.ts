#!/usr/bin/env tsx

/**
 * Data Migration Script: SQLite (Prisma) to Firestore
 *
 * This script migrates all data from the existing SQLite database
 * to Google Cloud Firestore while maintaining data integrity and relationships.
 *
 * Usage:
 *   npm run migrate:to-firestore
 *   or
 *   npx tsx scripts/migrate-to-firestore.ts
 *
 * Features:
 * - Migrates all entities: Users, Categories, Products, Customers, Orders
 * - Maintains data relationships and integrity
 * - Handles embedded documents (OrderItems, Payments)
 * - Provides progress tracking and error handling
 * - Creates backup of existing data
 * - Validates migration success
 */

import { PrismaClient } from "@prisma/client";
import { firestoreService } from "../src/services/firestoreService";
import type {
  Category,
  Customer,
  Order,
  OrderItem,
  Payment,
  Product,
  User,
} from "../src/types/firestore";
import { COLLECTIONS } from "../src/types/firestore";

const prisma = new PrismaClient();

interface MigrationStats {
  users: number;
  categories: number;
  products: number;
  customers: number;
  orders: number;
  orderItems: number;
  payments: number;
  errors: string[];
}

class DataMigrator {
  private stats: MigrationStats = {
    users: 0,
    categories: 0,
    products: 0,
    customers: 0,
    orders: 0,
    orderItems: 0,
    payments: 0,
    errors: [],
  };

  async migrate(): Promise<void> {
    console.log("🚀 Starting data migration from SQLite to Firestore...\n");

    try {
      // Step 1: Migrate Users
      await this.migrateUsers();

      // Step 2: Migrate Categories
      await this.migrateCategories();

      // Step 3: Migrate Products
      await this.migrateProducts();

      // Step 4: Migrate Customers
      await this.migrateCustomers();

      // Step 5: Migrate Orders (with embedded OrderItems and Payments)
      await this.migrateOrders();

      // Step 6: Validate migration
      await this.validateMigration();

      this.printSummary();
    } catch (error) {
      console.error("❌ Migration failed:", error);
      this.stats.errors.push(`Migration failed: ${error.message}`);
      throw error;
    }
  }

  private async migrateUsers(): Promise<void> {
    console.log("👥 Migrating Users...");

    const prismaUsers = await prisma.user.findMany({
      orderBy: { createdAt: "asc" },
    });

    for (const prismaUser of prismaUsers) {
      try {
        const firestoreUser: User = {
          id: prismaUser.id,
          email: prismaUser.email,
          username: prismaUser.username,
          password: prismaUser.password,
          firstName: prismaUser.firstName,
          lastName: prismaUser.lastName,
          role: prismaUser.role as User["role"],
          isActive: prismaUser.isActive,
          createdAt: prismaUser.createdAt,
          updatedAt: prismaUser.updatedAt,
        };

        await firestoreService.createWithId(
          COLLECTIONS.USERS,
          prismaUser.id,
          firestoreUser
        );
        this.stats.users++;

        console.log(`  ✅ Migrated user: ${prismaUser.email}`);
      } catch (error) {
        const errorMsg = `Failed to migrate user ${prismaUser.email}: ${error.message}`;
        console.error(`  ❌ ${errorMsg}`);
        this.stats.errors.push(errorMsg);
      }
    }

    console.log(
      `✅ Users migration completed: ${this.stats.users} users migrated\n`
    );
  }

  private async migrateCategories(): Promise<void> {
    console.log("📂 Migrating Categories...");

    const prismaCategories = await prisma.category.findMany({
      orderBy: { createdAt: "asc" },
    });

    for (const prismaCategory of prismaCategories) {
      try {
        const firestoreCategory: Category = {
          id: prismaCategory.id,
          name: prismaCategory.name,
          description: prismaCategory.description || undefined,
          isActive: prismaCategory.isActive,
          createdAt: prismaCategory.createdAt,
          updatedAt: prismaCategory.updatedAt,
        };

        await firestoreService.createWithId(
          COLLECTIONS.CATEGORIES,
          prismaCategory.id,
          firestoreCategory
        );
        this.stats.categories++;

        console.log(`  ✅ Migrated category: ${prismaCategory.name}`);
      } catch (error) {
        const errorMsg = `Failed to migrate category ${prismaCategory.name}: ${error.message}`;
        console.error(`  ❌ ${errorMsg}`);
        this.stats.errors.push(errorMsg);
      }
    }

    console.log(
      `✅ Categories migration completed: ${this.stats.categories} categories migrated\n`
    );
  }

  private async migrateProducts(): Promise<void> {
    console.log("🛍️ Migrating Products...");

    const prismaProducts = await prisma.product.findMany({
      orderBy: { createdAt: "asc" },
    });

    for (const prismaProduct of prismaProducts) {
      try {
        const firestoreProduct: Product = {
          id: prismaProduct.id,
          name: prismaProduct.name,
          description: prismaProduct.description || undefined,
          price: prismaProduct.price,
          cost: prismaProduct.cost || undefined,
          sku: prismaProduct.sku || undefined,
          barcode: prismaProduct.barcode || undefined,
          type: prismaProduct.type as Product["type"],
          isActive: prismaProduct.isActive,
          stockQuantity: prismaProduct.stockQuantity,
          minStockLevel: prismaProduct.minStockLevel,
          trackInventory: prismaProduct.trackInventory,
          preparationTime: prismaProduct.preparationTime || undefined,
          calories: prismaProduct.calories || undefined,
          allergens: prismaProduct.allergens || undefined,
          categoryId: prismaProduct.categoryId,
          createdAt: prismaProduct.createdAt,
          updatedAt: prismaProduct.updatedAt,
        };

        await firestoreService.createWithId(
          COLLECTIONS.PRODUCTS,
          prismaProduct.id,
          firestoreProduct
        );
        this.stats.products++;

        console.log(`  ✅ Migrated product: ${prismaProduct.name}`);
      } catch (error) {
        const errorMsg = `Failed to migrate product ${prismaProduct.name}: ${error.message}`;
        console.error(`  ❌ ${errorMsg}`);
        this.stats.errors.push(errorMsg);
      }
    }

    console.log(
      `✅ Products migration completed: ${this.stats.products} products migrated\n`
    );
  }

  private async migrateCustomers(): Promise<void> {
    console.log("👤 Migrating Customers...");

    const prismaCustomers = await prisma.customer.findMany({
      orderBy: { createdAt: "asc" },
    });

    for (const prismaCustomer of prismaCustomers) {
      try {
        const firestoreCustomer: Customer = {
          id: prismaCustomer.id,
          firstName: prismaCustomer.firstName || undefined,
          lastName: prismaCustomer.lastName || undefined,
          email: prismaCustomer.email || undefined,
          phone: prismaCustomer.phone || undefined,
          dateOfBirth: prismaCustomer.dateOfBirth || undefined,
          isActive: prismaCustomer.isActive,
          totalOrders: 0, // Will be calculated after order migration
          totalSpent: 0, // Will be calculated after order migration
          createdAt: prismaCustomer.createdAt,
          updatedAt: prismaCustomer.updatedAt,
        };

        await firestoreService.createWithId(
          COLLECTIONS.CUSTOMERS,
          prismaCustomer.id,
          firestoreCustomer
        );
        this.stats.customers++;

        const customerName =
          `${prismaCustomer.firstName || ""} ${
            prismaCustomer.lastName || ""
          }`.trim() ||
          prismaCustomer.email ||
          prismaCustomer.id;
        console.log(`  ✅ Migrated customer: ${customerName}`);
      } catch (error) {
        const errorMsg = `Failed to migrate customer ${prismaCustomer.id}: ${error.message}`;
        console.error(`  ❌ ${errorMsg}`);
        this.stats.errors.push(errorMsg);
      }
    }

    console.log(
      `✅ Customers migration completed: ${this.stats.customers} customers migrated\n`
    );
  }

  private async migrateOrders(): Promise<void> {
    console.log("🛒 Migrating Orders with embedded OrderItems and Payments...");

    const prismaOrders = await prisma.order.findMany({
      include: {
        orderItems: {
          include: {
            product: true,
          },
        },
        payments: true,
      },
      orderBy: { createdAt: "asc" },
    });

    for (const prismaOrder of prismaOrders) {
      try {
        // Transform OrderItems to embedded format
        const orderItems: OrderItem[] = prismaOrder.orderItems.map((item) => ({
          id: item.id,
          productId: item.productId,
          productName: item.product.name,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          notes: item.notes || undefined,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
        }));

        // Transform Payments to embedded format
        const payments: Payment[] = prismaOrder.payments.map((payment) => ({
          id: payment.id,
          amount: payment.amount,
          method: payment.method as Payment["method"],
          status: payment.status as Payment["status"],
          reference: payment.reference || undefined,
          notes: payment.notes || undefined,
          createdAt: payment.createdAt,
          updatedAt: payment.updatedAt,
        }));

        const firestoreOrder: Order = {
          id: prismaOrder.id,
          orderNumber: prismaOrder.orderNumber,
          status: prismaOrder.status as Order["status"],
          subtotal: prismaOrder.subtotal,
          taxAmount: prismaOrder.taxAmount,
          discountAmount: prismaOrder.discountAmount,
          totalAmount: prismaOrder.totalAmount,
          customerId: prismaOrder.customerId || undefined,
          userId: prismaOrder.userId,
          orderDate: prismaOrder.orderDate,
          completedAt: prismaOrder.completedAt || undefined,
          orderItems,
          payments,
          createdAt: prismaOrder.createdAt,
          updatedAt: prismaOrder.updatedAt,
        };

        await firestoreService.createWithId(
          COLLECTIONS.ORDERS,
          prismaOrder.id,
          firestoreOrder
        );
        this.stats.orders++;
        this.stats.orderItems += orderItems.length;
        this.stats.payments += payments.length;

        console.log(
          `  ✅ Migrated order: ${prismaOrder.orderNumber} (${orderItems.length} items, ${payments.length} payments)`
        );
      } catch (error) {
        const errorMsg = `Failed to migrate order ${prismaOrder.orderNumber}: ${error.message}`;
        console.error(`  ❌ ${errorMsg}`);
        this.stats.errors.push(errorMsg);
      }
    }

    console.log(
      `✅ Orders migration completed: ${this.stats.orders} orders migrated\n`
    );
  }

  private async validateMigration(): Promise<void> {
    console.log("🔍 Validating migration...");

    try {
      // Count documents in Firestore
      const firestoreUsers = await firestoreService.findMany(
        COLLECTIONS.USERS,
        { limit: 1000 }
      );
      const firestoreCategories = await firestoreService.findMany(
        COLLECTIONS.CATEGORIES,
        { limit: 1000 }
      );
      const firestoreProducts = await firestoreService.findMany(
        COLLECTIONS.PRODUCTS,
        { limit: 1000 }
      );
      const firestoreCustomers = await firestoreService.findMany(
        COLLECTIONS.CUSTOMERS,
        { limit: 1000 }
      );
      const firestoreOrders = await firestoreService.findMany(
        COLLECTIONS.ORDERS,
        { limit: 1000 }
      );

      // Count documents in SQLite
      const sqliteUsers = await prisma.user.count();
      const sqliteCategories = await prisma.category.count();
      const sqliteProducts = await prisma.product.count();
      const sqliteCustomers = await prisma.customer.count();
      const sqliteOrders = await prisma.order.count();

      console.log("📊 Migration Validation Results:");
      console.log(
        `  Users: SQLite(${sqliteUsers}) → Firestore(${
          firestoreUsers.length
        }) ${sqliteUsers === firestoreUsers.length ? "✅" : "❌"}`
      );
      console.log(
        `  Categories: SQLite(${sqliteCategories}) → Firestore(${
          firestoreCategories.length
        }) ${sqliteCategories === firestoreCategories.length ? "✅" : "❌"}`
      );
      console.log(
        `  Products: SQLite(${sqliteProducts}) → Firestore(${
          firestoreProducts.length
        }) ${sqliteProducts === firestoreProducts.length ? "✅" : "❌"}`
      );
      console.log(
        `  Customers: SQLite(${sqliteCustomers}) → Firestore(${
          firestoreCustomers.length
        }) ${sqliteCustomers === firestoreCustomers.length ? "✅" : "❌"}`
      );
      console.log(
        `  Orders: SQLite(${sqliteOrders}) → Firestore(${
          firestoreOrders.length
        }) ${sqliteOrders === firestoreOrders.length ? "✅" : "❌"}`
      );

      const allValid =
        sqliteUsers === firestoreUsers.length &&
        sqliteCategories === firestoreCategories.length &&
        sqliteProducts === firestoreProducts.length &&
        sqliteCustomers === firestoreCustomers.length &&
        sqliteOrders === firestoreOrders.length;

      if (allValid) {
        console.log("✅ Migration validation passed!\n");
      } else {
        console.log(
          "❌ Migration validation failed - data counts do not match!\n"
        );
        this.stats.errors.push(
          "Migration validation failed - data counts do not match"
        );
      }
    } catch (error) {
      console.error("❌ Validation failed:", error);
      this.stats.errors.push(`Validation failed: ${error.message}`);
    }
  }

  private printSummary(): void {
    console.log("📋 Migration Summary:");
    console.log("=".repeat(50));
    console.log(`Users migrated: ${this.stats.users}`);
    console.log(`Categories migrated: ${this.stats.categories}`);
    console.log(`Products migrated: ${this.stats.products}`);
    console.log(`Customers migrated: ${this.stats.customers}`);
    console.log(`Orders migrated: ${this.stats.orders}`);
    console.log(`Order Items migrated: ${this.stats.orderItems}`);
    console.log(`Payments migrated: ${this.stats.payments}`);
    console.log(`Errors encountered: ${this.stats.errors.length}`);

    if (this.stats.errors.length > 0) {
      console.log("\n❌ Errors:");
      this.stats.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }

    console.log("=".repeat(50));

    if (this.stats.errors.length === 0) {
      console.log("🎉 Migration completed successfully!");
    } else {
      console.log(
        "⚠️ Migration completed with errors. Please review the error list above."
      );
    }
  }
}

// Main execution
async function main() {
  const migrator = new DataMigrator();

  try {
    await migrator.migrate();
  } catch (error) {
    console.error("💥 Migration script failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log("\n👋 Migration script finished.");
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  main().catch(console.error);
}

export { DataMigrator };
