#!/usr/bin/env tsx
"use strict";
/**
 * Data Validation Script: SQLite vs Firestore
 *
 * This script validates data integrity between SQLite and Firestore
 * by comparing record counts, data consistency, and relationships.
 *
 * Usage:
 *   npm run migrate:validate
 *   or
 *   npx tsx scripts/validate-migration.ts
 *
 * Features:
 * - Compares record counts between databases
 * - Validates data integrity and consistency
 * - Checks relationship integrity
 * - Provides detailed validation report
 * - Identifies data discrepancies
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataValidator = void 0;
const client_1 = require("@prisma/client");
const firestoreService_1 = require("../src/services/firestoreService");
const firestore_1 = require("../src/types/firestore");
const prisma = new client_1.PrismaClient();
class DataValidator {
    constructor() {
        this.report = {
            results: [],
            overallValid: true,
            totalErrors: 0,
            summary: "",
        };
    }
    async validate() {
        console.log("🔍 Starting data validation between SQLite and Firestore...\n");
        try {
            // Validate each entity
            await this.validateUsers();
            await this.validateCategories();
            await this.validateProducts();
            await this.validateCustomers();
            await this.validateOrders();
            // Generate summary
            this.generateSummary();
            this.printReport();
            return this.report;
        }
        catch (error) {
            console.error("❌ Validation failed:", error);
            throw error;
        }
    }
    async validateUsers() {
        console.log("👥 Validating Users...");
        const result = {
            entity: "Users",
            sqliteCount: 0,
            firestoreCount: 0,
            isValid: true,
            discrepancies: [],
        };
        try {
            // Get counts
            result.sqliteCount = await prisma.user.count();
            const firestoreUsers = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.USERS, { limit: 1000 });
            result.firestoreCount = firestoreUsers.length;
            // Check count consistency
            if (result.sqliteCount !== result.firestoreCount) {
                result.isValid = false;
                result.discrepancies.push(`Count mismatch: SQLite(${result.sqliteCount}) vs Firestore(${result.firestoreCount})`);
            }
            // Sample data validation
            if (result.sqliteCount > 0 && result.firestoreCount > 0) {
                const sqliteUsers = await prisma.user.findMany({ take: 5 });
                for (const sqliteUser of sqliteUsers) {
                    const firestoreUser = firestoreUsers.find((u) => u.id === sqliteUser.id);
                    if (!firestoreUser) {
                        result.isValid = false;
                        result.discrepancies.push(`User ${sqliteUser.email} exists in SQLite but not in Firestore`);
                    }
                    else {
                        // Validate key fields
                        if (sqliteUser.email !== firestoreUser.email) {
                            result.isValid = false;
                            result.discrepancies.push(`User ${sqliteUser.id}: email mismatch`);
                        }
                        if (sqliteUser.role !== firestoreUser.role) {
                            result.isValid = false;
                            result.discrepancies.push(`User ${sqliteUser.id}: role mismatch`);
                        }
                    }
                }
            }
            console.log(`  ${result.isValid ? "✅" : "❌"} Users validation: ${result.sqliteCount} ↔ ${result.firestoreCount}`);
        }
        catch (error) {
            result.isValid = false;
            result.discrepancies.push(`Validation error: ${error.message}`);
            console.error(`  ❌ Users validation failed: ${error.message}`);
        }
        this.report.results.push(result);
        if (!result.isValid) {
            this.report.overallValid = false;
            this.report.totalErrors += result.discrepancies.length;
        }
    }
    async validateCategories() {
        console.log("📂 Validating Categories...");
        const result = {
            entity: "Categories",
            sqliteCount: 0,
            firestoreCount: 0,
            isValid: true,
            discrepancies: [],
        };
        try {
            result.sqliteCount = await prisma.category.count();
            const firestoreCategories = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.CATEGORIES, { limit: 1000 });
            result.firestoreCount = firestoreCategories.length;
            if (result.sqliteCount !== result.firestoreCount) {
                result.isValid = false;
                result.discrepancies.push(`Count mismatch: SQLite(${result.sqliteCount}) vs Firestore(${result.firestoreCount})`);
            }
            // Sample data validation
            if (result.sqliteCount > 0 && result.firestoreCount > 0) {
                const sqliteCategories = await prisma.category.findMany({ take: 5 });
                for (const sqliteCategory of sqliteCategories) {
                    const firestoreCategory = firestoreCategories.find((c) => c.id === sqliteCategory.id);
                    if (!firestoreCategory) {
                        result.isValid = false;
                        result.discrepancies.push(`Category ${sqliteCategory.name} exists in SQLite but not in Firestore`);
                    }
                    else {
                        if (sqliteCategory.name !== firestoreCategory.name) {
                            result.isValid = false;
                            result.discrepancies.push(`Category ${sqliteCategory.id}: name mismatch`);
                        }
                    }
                }
            }
            console.log(`  ${result.isValid ? "✅" : "❌"} Categories validation: ${result.sqliteCount} ↔ ${result.firestoreCount}`);
        }
        catch (error) {
            result.isValid = false;
            result.discrepancies.push(`Validation error: ${error.message}`);
            console.error(`  ❌ Categories validation failed: ${error.message}`);
        }
        this.report.results.push(result);
        if (!result.isValid) {
            this.report.overallValid = false;
            this.report.totalErrors += result.discrepancies.length;
        }
    }
    async validateProducts() {
        console.log("🛍️ Validating Products...");
        const result = {
            entity: "Products",
            sqliteCount: 0,
            firestoreCount: 0,
            isValid: true,
            discrepancies: [],
        };
        try {
            result.sqliteCount = await prisma.product.count();
            const firestoreProducts = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.PRODUCTS, { limit: 1000 });
            result.firestoreCount = firestoreProducts.length;
            if (result.sqliteCount !== result.firestoreCount) {
                result.isValid = false;
                result.discrepancies.push(`Count mismatch: SQLite(${result.sqliteCount}) vs Firestore(${result.firestoreCount})`);
            }
            // Sample data validation
            if (result.sqliteCount > 0 && result.firestoreCount > 0) {
                const sqliteProducts = await prisma.product.findMany({ take: 5 });
                for (const sqliteProduct of sqliteProducts) {
                    const firestoreProduct = firestoreProducts.find((p) => p.id === sqliteProduct.id);
                    if (!firestoreProduct) {
                        result.isValid = false;
                        result.discrepancies.push(`Product ${sqliteProduct.name} exists in SQLite but not in Firestore`);
                    }
                    else {
                        if (sqliteProduct.name !== firestoreProduct.name) {
                            result.isValid = false;
                            result.discrepancies.push(`Product ${sqliteProduct.id}: name mismatch`);
                        }
                        if (sqliteProduct.price !== firestoreProduct.price) {
                            result.isValid = false;
                            result.discrepancies.push(`Product ${sqliteProduct.id}: price mismatch`);
                        }
                        if (sqliteProduct.categoryId !== firestoreProduct.categoryId) {
                            result.isValid = false;
                            result.discrepancies.push(`Product ${sqliteProduct.id}: categoryId mismatch`);
                        }
                    }
                }
            }
            console.log(`  ${result.isValid ? "✅" : "❌"} Products validation: ${result.sqliteCount} ↔ ${result.firestoreCount}`);
        }
        catch (error) {
            result.isValid = false;
            result.discrepancies.push(`Validation error: ${error.message}`);
            console.error(`  ❌ Products validation failed: ${error.message}`);
        }
        this.report.results.push(result);
        if (!result.isValid) {
            this.report.overallValid = false;
            this.report.totalErrors += result.discrepancies.length;
        }
    }
    async validateCustomers() {
        console.log("👤 Validating Customers...");
        const result = {
            entity: "Customers",
            sqliteCount: 0,
            firestoreCount: 0,
            isValid: true,
            discrepancies: [],
        };
        try {
            result.sqliteCount = await prisma.customer.count();
            const firestoreCustomers = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.CUSTOMERS, { limit: 1000 });
            result.firestoreCount = firestoreCustomers.length;
            if (result.sqliteCount !== result.firestoreCount) {
                result.isValid = false;
                result.discrepancies.push(`Count mismatch: SQLite(${result.sqliteCount}) vs Firestore(${result.firestoreCount})`);
            }
            console.log(`  ${result.isValid ? "✅" : "❌"} Customers validation: ${result.sqliteCount} ↔ ${result.firestoreCount}`);
        }
        catch (error) {
            result.isValid = false;
            result.discrepancies.push(`Validation error: ${error.message}`);
            console.error(`  ❌ Customers validation failed: ${error.message}`);
        }
        this.report.results.push(result);
        if (!result.isValid) {
            this.report.overallValid = false;
            this.report.totalErrors += result.discrepancies.length;
        }
    }
    async validateOrders() {
        console.log("🛒 Validating Orders...");
        const result = {
            entity: "Orders",
            sqliteCount: 0,
            firestoreCount: 0,
            isValid: true,
            discrepancies: [],
        };
        try {
            result.sqliteCount = await prisma.order.count();
            const firestoreOrders = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.ORDERS, { limit: 1000 });
            result.firestoreCount = firestoreOrders.length;
            if (result.sqliteCount !== result.firestoreCount) {
                result.isValid = false;
                result.discrepancies.push(`Count mismatch: SQLite(${result.sqliteCount}) vs Firestore(${result.firestoreCount})`);
            }
            // Validate order items and payments embedding
            if (result.sqliteCount > 0 && result.firestoreCount > 0) {
                const sqliteOrderItems = await prisma.orderItem.count();
                const sqlitePayments = await prisma.payment.count();
                let firestoreOrderItems = 0;
                let firestorePayments = 0;
                firestoreOrders.forEach((order) => {
                    firestoreOrderItems += order.orderItems?.length || 0;
                    firestorePayments += order.payments?.length || 0;
                });
                if (sqliteOrderItems !== firestoreOrderItems) {
                    result.isValid = false;
                    result.discrepancies.push(`Order Items count mismatch: SQLite(${sqliteOrderItems}) vs Firestore(${firestoreOrderItems})`);
                }
                if (sqlitePayments !== firestorePayments) {
                    result.isValid = false;
                    result.discrepancies.push(`Payments count mismatch: SQLite(${sqlitePayments}) vs Firestore(${firestorePayments})`);
                }
            }
            console.log(`  ${result.isValid ? "✅" : "❌"} Orders validation: ${result.sqliteCount} ↔ ${result.firestoreCount}`);
        }
        catch (error) {
            result.isValid = false;
            result.discrepancies.push(`Validation error: ${error.message}`);
            console.error(`  ❌ Orders validation failed: ${error.message}`);
        }
        this.report.results.push(result);
        if (!result.isValid) {
            this.report.overallValid = false;
            this.report.totalErrors += result.discrepancies.length;
        }
    }
    generateSummary() {
        const totalEntities = this.report.results.length;
        const validEntities = this.report.results.filter((r) => r.isValid).length;
        this.report.summary = `Validation completed: ${validEntities}/${totalEntities} entities valid, ${this.report.totalErrors} total errors`;
    }
    printReport() {
        console.log("\n📋 Validation Report:");
        console.log("=".repeat(60));
        this.report.results.forEach((result) => {
            console.log(`${result.isValid ? "✅" : "❌"} ${result.entity}: SQLite(${result.sqliteCount}) ↔ Firestore(${result.firestoreCount})`);
            if (result.discrepancies.length > 0) {
                result.discrepancies.forEach((discrepancy) => {
                    console.log(`    ⚠️  ${discrepancy}`);
                });
            }
        });
        console.log("=".repeat(60));
        console.log(`📊 ${this.report.summary}`);
        if (this.report.overallValid) {
            console.log("🎉 All validations passed! Data migration is consistent.");
        }
        else {
            console.log("❌ Validation failed! Please review the discrepancies above.");
        }
    }
}
exports.DataValidator = DataValidator;
// Main execution
async function main() {
    const validator = new DataValidator();
    try {
        const report = await validator.validate();
        // Exit with error code if validation failed
        if (!report.overallValid) {
            process.exit(1);
        }
    }
    catch (error) {
        console.error("💥 Validation script failed:", error);
        process.exit(1);
    }
    finally {
        await prisma.$disconnect();
        console.log("\n👋 Validation script finished.");
    }
}
// Run the validation if this script is executed directly
if (require.main === module) {
    main().catch(console.error);
}
