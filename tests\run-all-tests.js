#!/usr/bin/env tsx
"use strict";
/**
 * Comprehensive Test Runner
 *
 * This script runs all validation tests for the Firestore-migrated POS backend:
 * - API endpoint tests
 * - Firestore-specific validation
 * - Security feature tests
 * - Performance benchmarks
 *
 * Usage:
 *   npm run test:all
 *   or
 *   npx tsx tests/run-all-tests.ts
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComprehensiveTestRunner = void 0;
const api_tests_1 = require("./api-tests");
const firestore_validation_1 = require("./firestore-validation");
const setup_1 = require("./setup");
class ComprehensiveTestRunner {
    constructor() {
        this.results = [];
    }
    async runAllTests() {
        console.log('🚀 Starting Comprehensive POS Backend Testing Suite');
        console.log('='.repeat(60));
        console.log(`📍 Target: ${setup_1.TEST_CONFIG.baseURL}`);
        console.log(`🕐 Started: ${new Date().toISOString()}`);
        console.log('='.repeat(60));
        console.log();
        try {
            // Run test suites in sequence
            await this.runTestSuite('Server Health Check', this.checkServerHealth.bind(this));
            await this.runTestSuite('API Endpoint Tests', this.runAPITests.bind(this));
            await this.runTestSuite('Firestore Validation', this.runFirestoreValidation.bind(this));
            await this.runTestSuite('Performance Benchmarks', this.runPerformanceBenchmarks.bind(this));
            this.printFinalSummary();
        }
        catch (error) {
            console.error('💥 Test suite execution failed:', error);
            throw error;
        }
    }
    async runTestSuite(name, testFn) {
        console.log(`\n🧪 Running ${name}...`);
        console.log('-'.repeat(40));
        const startTime = Date.now();
        try {
            const result = await testFn();
            const duration = Date.now() - startTime;
            this.results.push({
                name,
                status: 'PASS',
                duration,
                details: result
            });
            console.log(`✅ ${name} completed successfully (${duration}ms)`);
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.results.push({
                name,
                status: 'FAIL',
                duration,
                error: error.message
            });
            console.log(`❌ ${name} failed (${duration}ms): ${error.message}`);
        }
    }
    async checkServerHealth() {
        const axios = (await Promise.resolve().then(() => __importStar(require('axios')))).default;
        try {
            const response = await axios.get(`${setup_1.TEST_CONFIG.baseURL}/health`, {
                timeout: 5000
            });
            if (response.status !== 200) {
                throw new Error(`Server health check failed: ${response.status}`);
            }
            const data = response.data;
            if (!data.status || data.status !== 'OK') {
                throw new Error('Server health status is not OK');
            }
            return {
                status: data.status,
                uptime: data.uptime,
                timestamp: data.timestamp
            };
        }
        catch (error) {
            if (error.code === 'ECONNREFUSED') {
                throw new Error('Server is not running or not accessible');
            }
            throw error;
        }
    }
    async runAPITests() {
        const apiTester = new api_tests_1.APITester();
        try {
            await apiTester.runAllTests();
            return { message: 'All API tests completed' };
        }
        catch (error) {
            throw new Error(`API tests failed: ${error.message}`);
        }
    }
    async runFirestoreValidation() {
        const firestoreValidator = new firestore_validation_1.FirestoreValidator();
        try {
            await firestoreValidator.runValidation();
            return { message: 'All Firestore validation tests completed' };
        }
        catch (error) {
            throw new Error(`Firestore validation failed: ${error.message}`);
        }
    }
    async runPerformanceBenchmarks() {
        console.log('  📊 Running performance benchmarks...');
        const axios = (await Promise.resolve().then(() => __importStar(require('axios')))).default;
        const client = axios.create({
            baseURL: setup_1.TEST_CONFIG.baseURL,
            timeout: 30000
        });
        const benchmarks = [];
        // Health endpoint benchmark
        const healthStart = Date.now();
        for (let i = 0; i < 10; i++) {
            await client.get('/health');
        }
        const healthDuration = Date.now() - healthStart;
        benchmarks.push({
            test: 'Health endpoint (10 requests)',
            duration: healthDuration,
            avgPerRequest: healthDuration / 10
        });
        // Concurrent requests benchmark
        const concurrentStart = Date.now();
        const concurrentPromises = Array(5).fill(0).map(() => client.get('/health'));
        await Promise.all(concurrentPromises);
        const concurrentDuration = Date.now() - concurrentStart;
        benchmarks.push({
            test: 'Concurrent requests (5 parallel)',
            duration: concurrentDuration,
            avgPerRequest: concurrentDuration / 5
        });
        return {
            benchmarks,
            summary: {
                totalTests: benchmarks.length,
                avgResponseTime: benchmarks.reduce((sum, b) => sum + b.avgPerRequest, 0) / benchmarks.length
            }
        };
    }
    printFinalSummary() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 COMPREHENSIVE TEST RESULTS SUMMARY');
        console.log('='.repeat(60));
        const totalSuites = this.results.length;
        const passedSuites = this.results.filter(r => r.status === 'PASS').length;
        const failedSuites = this.results.filter(r => r.status === 'FAIL').length;
        const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
        const passRate = totalSuites > 0 ? (passedSuites / totalSuites * 100).toFixed(1) : '0.0';
        console.log(`\n📈 Overall Results:`);
        console.log(`   Test Suites: ${totalSuites}`);
        console.log(`   Passed: ${passedSuites}`);
        console.log(`   Failed: ${failedSuites}`);
        console.log(`   Pass Rate: ${passRate}%`);
        console.log(`   Total Duration: ${(totalDuration / 1000).toFixed(2)}s`);
        console.log(`\n📋 Suite Details:`);
        this.results.forEach(result => {
            const status = result.status === 'PASS' ? '✅' : '❌';
            const duration = (result.duration / 1000).toFixed(2);
            console.log(`   ${status} ${result.name} (${duration}s)`);
            if (result.status === 'FAIL' && result.error) {
                console.log(`      Error: ${result.error}`);
            }
        });
        if (failedSuites === 0) {
            console.log('\n🎉 ALL TESTS PASSED!');
            console.log('✅ The Firestore migration is successful and fully functional');
            console.log('✅ All API endpoints are working correctly');
            console.log('✅ Firestore integration is properly implemented');
            console.log('✅ Security features are functioning as expected');
            console.log('✅ Performance benchmarks are within acceptable ranges');
            console.log('\n🚀 The POS backend system is ready for production deployment!');
        }
        else {
            console.log(`\n⚠️  ${failedSuites} test suite(s) failed.`);
            console.log('❌ Please review and fix the issues before deployment.');
            console.log('\n🔧 Recommended Actions:');
            this.results.filter(r => r.status === 'FAIL').forEach(result => {
                console.log(`   • Fix issues in: ${result.name}`);
            });
        }
        console.log('\n' + '='.repeat(60));
        console.log(`🕐 Completed: ${new Date().toISOString()}`);
        console.log('='.repeat(60));
    }
}
exports.ComprehensiveTestRunner = ComprehensiveTestRunner;
// Main execution
async function main() {
    const runner = new ComprehensiveTestRunner();
    try {
        await runner.runAllTests();
        // Exit with appropriate code
        process.exit(0);
    }
    catch (error) {
        console.error('\n💥 Test execution failed:', error);
        console.log('\n🔧 Troubleshooting:');
        console.log('   1. Ensure the server is running: npm run dev');
        console.log('   2. Check environment configuration');
        console.log('   3. Verify Firestore credentials');
        console.log('   4. Review server logs for errors');
        process.exit(1);
    }
}
// Run tests if this script is executed directly
if (require.main === module) {
    main().catch(console.error);
}
