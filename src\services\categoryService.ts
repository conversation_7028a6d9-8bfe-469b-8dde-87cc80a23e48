import { logger } from "../config/logger";
import { CustomError } from "../middleware/errorHandler";
import {
  CategoryQuery,
  CreateCategoryInput,
  UpdateCategoryInput,
} from "../schemas/category";

export class CategoryService {
  static async createCategory(data: CreateCategoryInput) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Category = import("../types/firestore").Category;

      // Check if category already exists
      const existingCategories = await firestoreService.findMany<Category>(
        COLLECTIONS.CATEGORIES,
        {
          where: [{ field: "name", operator: "==", value: data.name }],
          limit: 1,
        }
      );

      if (existingCategories.length > 0) {
        throw new CustomError("Category with this name already exists", 409);
      }

      // Create category data with default values
      const categoryData = {
        ...data,
        isActive: true,
      };

      const category = await firestoreService.create<Category>(
        COLLECTIONS.CATEGORIES,
        categoryData
      );

      logger.info(`Category created: ${category.name}`);
      return category;
    } catch (error) {
      logger.error("Category creation failed:", error);
      throw error;
    }
  }

  static async getCategories(query: CategoryQuery) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Category = import("../types/firestore").Category;

      const { page, limit, search, isActive } = query;

      // Build where conditions for Firestore
      const whereConditions: Array<{
        field: string;
        operator: any;
        value: any;
      }> = [];

      // Filter by active status
      if (isActive !== undefined) {
        whereConditions.push({
          field: "isActive",
          operator: "==",
          value: isActive,
        });
      }

      let categories: Category[];
      let total: number;

      // Handle search functionality
      if (search) {
        // Simple search on category name
        const searchResults = await firestoreService.search<Category>(
          COLLECTIONS.CATEGORIES,
          "name",
          search.toLowerCase(),
          {
            limit: 1000,
            additionalWhere: whereConditions,
          }
        );

        // Filter results manually for better search
        const filteredCategories = searchResults.filter(
          (category: Category) => {
            const searchLower = search.toLowerCase();
            return (
              category.name.toLowerCase().includes(searchLower) ||
              (category.description &&
                category.description.toLowerCase().includes(searchLower))
            );
          }
        );

        total = filteredCategories.length;
        const skip = (page - 1) * limit;
        categories = filteredCategories.slice(skip, skip + limit);
      } else {
        // Use pagination for regular queries
        const result = await firestoreService.findManyWithPagination<Category>(
          COLLECTIONS.CATEGORIES,
          {
            page,
            limit,
            where: whereConditions,
            orderBy: { field: "createdAt", direction: "desc" },
          }
        );

        categories = result.data;
        total = result.pagination.total;
      }

      return {
        categories,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error("Failed to fetch categories:", error);
      throw error;
    }
  }

  static async getCategoryById(id: string) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Category = import("../types/firestore").Category;

      const category = await firestoreService.findById<Category>(
        COLLECTIONS.CATEGORIES,
        id
      );

      if (!category) {
        throw new CustomError("Category not found", 404);
      }

      // Note: In Firestore, we don't have joins like Prisma
      // Product count would need to be calculated separately if needed
      return category;
    } catch (error) {
      logger.error("Failed to fetch category:", error);
      throw error;
    }
  }

  static async updateCategory(id: string, data: UpdateCategoryInput) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Category = import("../types/firestore").Category;

      const existingCategory = await firestoreService.findById<Category>(
        COLLECTIONS.CATEGORIES,
        id
      );

      if (!existingCategory) {
        throw new CustomError("Category not found", 404);
      }

      // Check for name conflicts
      if (data.name) {
        const conflictCategories = await firestoreService.findMany<Category>(
          COLLECTIONS.CATEGORIES,
          {
            where: [{ field: "name", operator: "==", value: data.name }],
            limit: 1,
          }
        );

        if (conflictCategories.length > 0 && conflictCategories[0].id !== id) {
          throw new CustomError("Category name already exists", 409);
        }
      }

      const category = await firestoreService.update<Category>(
        COLLECTIONS.CATEGORIES,
        id,
        data
      );

      logger.info(`Category updated: ${category.name}`);
      return category;
    } catch (error) {
      logger.error("Category update failed:", error);
      throw error;
    }
  }

  static async deleteCategory(id: string) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Category = import("../types/firestore").Category;
      type Product = import("../types/firestore").Product;

      const category = await firestoreService.findById<Category>(
        COLLECTIONS.CATEGORIES,
        id
      );

      if (!category) {
        throw new CustomError("Category not found", 404);
      }

      // Check if category has products
      const categoryProducts = await firestoreService.findMany<Product>(
        COLLECTIONS.PRODUCTS,
        {
          where: [{ field: "categoryId", operator: "==", value: id }],
          limit: 1,
        }
      );

      if (categoryProducts.length > 0) {
        throw new CustomError(
          "Cannot delete category with existing products",
          400
        );
      }

      await firestoreService.delete(COLLECTIONS.CATEGORIES, id);

      logger.info(`Category deleted: ${category.name}`);
      return { message: "Category deleted successfully" };
    } catch (error) {
      logger.error("Category deletion failed:", error);
      throw error;
    }
  }
}
