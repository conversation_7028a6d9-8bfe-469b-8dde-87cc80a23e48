import { logger } from "../config/logger";
import { CustomError } from "../middleware/errorHandler";
import {
  CreateOrderInput,
  CreatePaymentInput,
  OrderQuery,
  UpdateOrderInput,
  UpdatePaymentInput,
} from "../schemas/order";
import { OrderStatus, PaymentStatus } from "../types/firestore";

export class OrderService {
  static async createOrder(data: CreateOrderInput, userId: string) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Order = import("../types/firestore").Order;
      type Product = import("../types/firestore").Product;
      type Customer = import("../types/firestore").Customer;
      type OrderItem = import("../types/firestore").OrderItem;

      return await firestoreService.runTransaction(async (transaction) => {
        // Validate customer if provided
        if (data.customerId) {
          const customer = await firestoreService.findById<Customer>(
            COLLECTIONS.CUSTOMERS,
            data.customerId
          );

          if (!customer || !customer.isActive) {
            throw new CustomError("Customer not found or inactive", 404);
          }
        }

        // Validate products and calculate totals
        let subtotal = 0;
        const orderItems: OrderItem[] = [];

        for (const item of data.items) {
          const product = await firestoreService.findById<Product>(
            COLLECTIONS.PRODUCTS,
            item.productId
          );

          if (!product || !product.isActive) {
            throw new CustomError(
              `Product not found or inactive: ${item.productId}`,
              404
            );
          }

          // Check inventory if tracking is enabled
          if (product.trackInventory && product.stockQuantity < item.quantity) {
            throw new CustomError(
              `Insufficient stock for product: ${product.name}. Available: ${product.stockQuantity}, Requested: ${item.quantity}`,
              400
            );
          }

          const itemTotal = product.price * item.quantity;
          subtotal += itemTotal;

          orderItems.push({
            productId: item.productId,
            productName: product.name,
            quantity: item.quantity,
            unitPrice: product.price,
            totalPrice: itemTotal,
            notes: item.notes,
          });

          // Update inventory if tracking is enabled
          if (product.trackInventory) {
            await firestoreService.update<Product>(
              COLLECTIONS.PRODUCTS,
              item.productId,
              {
                stockQuantity: product.stockQuantity - item.quantity,
              }
            );
          }
        }

        // Calculate total amount
        const totalAmount = subtotal + data.taxAmount - data.discountAmount;

        if (totalAmount < 0) {
          throw new CustomError("Total amount cannot be negative", 400);
        }

        // Generate order number
        const orderCount = await firestoreService.count(COLLECTIONS.ORDERS);
        const orderNumber = `ORD-${Date.now()}-${String(
          orderCount + 1
        ).padStart(4, "0")}`;

        // Create order data
        const orderData = {
          orderNumber,
          status: OrderStatus.PENDING,
          customerId: data.customerId,
          customerName: data.customerId ? undefined : "Walk-in Customer",
          items: orderItems,
          subtotal,
          taxAmount: data.taxAmount,
          discountAmount: data.discountAmount,
          totalAmount,
          payments: [],
          paidAmount: 0,
          changeAmount: 0,
          notes: data.notes,
          orderType: "DINE_IN" as const,
          createdBy: userId,
        };

        // Create order
        const order = await firestoreService.create<Order>(
          COLLECTIONS.ORDERS,
          orderData
        );

        logger.info(
          `Order created: ${order.orderNumber} (ID: ${order.id}) by user ${userId}`
        );
        return order;
      });
    } catch (error) {
      logger.error("Order creation failed:", error);
      throw error;
    }
  }

  static async getOrders(query: OrderQuery) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Order = import("../types/firestore").Order;

      const {
        page,
        limit,
        search,
        status,
        customerId,
        userId,
        startDate,
        endDate,
        minAmount,
        maxAmount,
      } = query;

      // Build where conditions for Firestore
      const whereConditions: Array<{
        field: string;
        operator: any;
        value: any;
      }> = [];

      // Filter by status
      if (status) {
        whereConditions.push({
          field: "status",
          operator: "==",
          value: status,
        });
      }

      // Filter by customer
      if (customerId) {
        whereConditions.push({
          field: "customerId",
          operator: "==",
          value: customerId,
        });
      }

      // Filter by user
      if (userId) {
        whereConditions.push({
          field: "createdBy",
          operator: "==",
          value: userId,
        });
      }

      // Date range filter using Firestore Timestamp
      if (startDate) {
        const { Timestamp } = await import("firebase-admin/firestore");
        whereConditions.push({
          field: "createdAt",
          operator: ">=",
          value: Timestamp.fromDate(new Date(startDate)),
        });
      }
      if (endDate) {
        const { Timestamp } = await import("firebase-admin/firestore");
        whereConditions.push({
          field: "createdAt",
          operator: "<=",
          value: Timestamp.fromDate(new Date(endDate)),
        });
      }

      // Amount range filter
      if (minAmount !== undefined) {
        whereConditions.push({
          field: "totalAmount",
          operator: ">=",
          value: minAmount,
        });
      }
      if (maxAmount !== undefined) {
        whereConditions.push({
          field: "totalAmount",
          operator: "<=",
          value: maxAmount,
        });
      }

      let orders: Order[];
      let total: number;

      // Handle search functionality
      if (search) {
        // Simple search on order number
        const searchResults = await firestoreService.search<Order>(
          COLLECTIONS.ORDERS,
          "orderNumber",
          search.toLowerCase(),
          {
            limit: 1000,
            additionalWhere: whereConditions,
          }
        );

        total = searchResults.length;
        const skip = (page - 1) * limit;
        orders = searchResults.slice(skip, skip + limit);
      } else {
        // Use pagination for regular queries
        const result = await firestoreService.findManyWithPagination<Order>(
          COLLECTIONS.ORDERS,
          {
            page,
            limit,
            where: whereConditions,
            orderBy: { field: "createdAt", direction: "desc" },
          }
        );

        orders = result.data;
        total = result.pagination.total;
      }

      return {
        orders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error("Failed to fetch orders:", error);
      throw error;
    }
  }

  static async getOrderById(id: string) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Order = import("../types/firestore").Order;

      const order = await firestoreService.findById<Order>(
        COLLECTIONS.ORDERS,
        id
      );

      if (!order) {
        throw new CustomError("Order not found", 404);
      }

      // Note: In Firestore, order items and payments are embedded in the order document
      // Customer and user information would need to be fetched separately if needed
      // For now, we return the order as-is since it contains all the embedded data

      return order;
    } catch (error) {
      logger.error("Failed to fetch order:", error);
      throw error;
    }
  }

  static async updateOrder(id: string, data: UpdateOrderInput) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Order = import("../types/firestore").Order;
      type Customer = import("../types/firestore").Customer;

      const existingOrder = await firestoreService.findById<Order>(
        COLLECTIONS.ORDERS,
        id
      );

      if (!existingOrder) {
        throw new CustomError("Order not found", 404);
      }

      // Validate status transitions
      if (data.status) {
        const validTransitions = this.getValidStatusTransitions(
          existingOrder.status
        );
        if (!validTransitions.includes(data.status)) {
          throw new CustomError(
            `Invalid status transition from ${existingOrder.status} to ${data.status}`,
            400
          );
        }
      }

      // Validate customer if being updated
      if (data.customerId) {
        const customer = await firestoreService.findById<Customer>(
          COLLECTIONS.CUSTOMERS,
          data.customerId
        );

        if (!customer || !customer.isActive) {
          throw new CustomError("Customer not found or inactive", 404);
        }
      }

      // Recalculate total if tax or discount changed
      let updateData: any = { ...data };
      if (data.taxAmount !== undefined || data.discountAmount !== undefined) {
        const taxAmount = data.taxAmount ?? existingOrder.taxAmount;
        const discountAmount =
          data.discountAmount ?? existingOrder.discountAmount;
        const totalAmount = existingOrder.subtotal + taxAmount - discountAmount;

        if (totalAmount < 0) {
          throw new CustomError("Total amount cannot be negative", 400);
        }

        updateData.totalAmount = totalAmount;
      }

      const order = await firestoreService.update<Order>(
        COLLECTIONS.ORDERS,
        id,
        updateData
      );

      logger.info(`Order updated: ${order.orderNumber} (ID: ${order.id})`);
      return order;
    } catch (error) {
      logger.error("Order update failed:", error);
      throw error;
    }
  }

  static async createPayment(orderId: string, data: CreatePaymentInput) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Order = import("../types/firestore").Order;
      type Payment = import("../types/firestore").Payment;

      const order = await firestoreService.findById<Order>(
        COLLECTIONS.ORDERS,
        orderId
      );

      if (!order) {
        throw new CustomError("Order not found", 404);
      }

      // Calculate total paid amount
      const totalPaid = order.payments
        .filter((p: any) => p.status === PaymentStatus.COMPLETED)
        .reduce((sum: number, p: any) => sum + p.amount, 0);

      const remainingAmount = order.totalAmount - totalPaid;

      if (data.amount > remainingAmount) {
        throw new CustomError(
          `Payment amount (${data.amount}) exceeds remaining balance (${remainingAmount})`,
          400
        );
      }

      // Create payment data
      const paymentData = {
        ...data,
        orderId,
        status: PaymentStatus.COMPLETED, // Auto-complete for now
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Add payment to order's payments array
      const updatedPayments = [...order.payments, paymentData];

      // Check if order is fully paid
      const newTotalPaid = totalPaid + data.amount;
      let updateData: any = { payments: updatedPayments };

      if (
        newTotalPaid >= order.totalAmount &&
        order.status === OrderStatus.PENDING
      ) {
        updateData.status = OrderStatus.PREPARING;
      }

      // Update order with new payment
      await firestoreService.update<Order>(
        COLLECTIONS.ORDERS,
        orderId,
        updateData
      );

      logger.info(
        `Payment created for order ${order.orderNumber}: ${data.amount} via ${data.method}`
      );
      return paymentData;
    } catch (error) {
      logger.error("Payment creation failed:", error);
      throw error;
    }
  }

  static async updatePayment(
    orderId: string,
    paymentId: string,
    data: UpdatePaymentInput
  ) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Order = import("../types/firestore").Order;

      const order = await firestoreService.findById<Order>(
        COLLECTIONS.ORDERS,
        orderId
      );

      if (!order) {
        throw new CustomError("Order not found", 404);
      }

      // Find payment in order's payments array
      const paymentIndex = order.payments.findIndex(
        (p: any) => p.id === paymentId
      );

      if (paymentIndex === -1) {
        throw new CustomError("Payment not found", 404);
      }

      // Update payment in the array
      const updatedPayments = [...order.payments];
      updatedPayments[paymentIndex] = {
        ...updatedPayments[paymentIndex],
        ...data,
        updatedAt: new Date(),
      };

      // Update order with modified payments
      await firestoreService.update<Order>(COLLECTIONS.ORDERS, orderId, {
        payments: updatedPayments,
      });

      logger.info(`Payment updated: ${paymentId} for order ${orderId}`);
      return updatedPayments[paymentIndex];
    } catch (error) {
      logger.error("Payment update failed:", error);
      throw error;
    }
  }

  private static getValidStatusTransitions(
    currentStatus: OrderStatus
  ): OrderStatus[] {
    const transitions: Record<OrderStatus, OrderStatus[]> = {
      [OrderStatus.PENDING]: [OrderStatus.PREPARING, OrderStatus.CANCELLED],
      [OrderStatus.PREPARING]: [OrderStatus.READY, OrderStatus.CANCELLED],
      [OrderStatus.READY]: [OrderStatus.COMPLETED, OrderStatus.CANCELLED],
      [OrderStatus.COMPLETED]: [], // No transitions from completed
      [OrderStatus.CANCELLED]: [], // No transitions from cancelled
    };

    return transitions[currentStatus] || [];
  }
}
