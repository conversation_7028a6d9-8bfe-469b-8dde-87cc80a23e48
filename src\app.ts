import cors from "cors";
import dotenv from "dotenv";
import express from "express";
import { logger } from "./config/logger";
import { errorHandler } from "./middleware/errorHandler";
import { requestLogger } from "./middleware/requestLogger";
import {
  authLimiter,
  generalLimiter,
  requestSizeLimiter,
  sanitizeInput,
  securityHeaders,
} from "./middleware/security";

// Import routes
import authRoutes from "./routes/auth";
import categoryRoutes from "./routes/categories";
import customerRoutes from "./routes/customers";
import orderRoutes from "./routes/orders";
import productRoutes from "./routes/products";
import userRoutes from "./routes/users";

// Load environment variables
dotenv.config();

// Initialize security configuration
import { initializeSecurity } from "./config/security";
initializeSecurity();

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(securityHeaders);
app.use(requestSizeLimiter);
app.use(sanitizeInput);
app.use(generalLimiter);

// Basic middleware
app.use(cors());
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));
app.use(requestLogger);

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || "development",
  });
});

// API Routes with specific rate limiting
app.use("/api/auth", authLimiter, authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/categories", categoryRoutes);
app.use("/api/products", productRoutes);
app.use("/api/orders", orderRoutes);
app.use("/api/customers", customerRoutes);

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({
    success: false,
    message: "Route not found",
    path: req.originalUrl,
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  logger.info(`🚀 POS Backend Server running on port ${PORT}`);
  logger.info(`📊 Environment: ${process.env.NODE_ENV || "development"}`);
  logger.info(`🔗 Health check: http://localhost:${PORT}/health`);
});

export default app;
