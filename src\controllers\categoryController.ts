import { Request, Response } from 'express';
import { CategoryService } from '../services/categoryService';
import { asyncHandler } from '../middleware/errorHandler';
import { CreateCategoryInput, UpdateCategoryInput, CategoryParams, CategoryQuery } from '../schemas/category';

export class CategoryController {
  static createCategory = asyncHandler(async (req: Request, res: Response) => {
    const data: CreateCategoryInput = req.body;
    const category = await CategoryService.createCategory(data);
    
    res.status(201).json({
      success: true,
      message: 'Category created successfully',
      data: category
    });
  });

  static getCategories = asyncHandler(async (req: Request, res: Response) => {
    const query: CategoryQuery = req.query as any;
    const result = await CategoryService.getCategories(query);
    
    res.status(200).json({
      success: true,
      message: 'Categories retrieved successfully',
      data: result.categories,
      pagination: result.pagination
    });
  });

  static getCategoryById = asyncHandler(async (req: Request, res: Response) => {
    const { id }: CategoryParams = req.params as any;
    const category = await CategoryService.getCategoryById(id);
    
    res.status(200).json({
      success: true,
      message: 'Category retrieved successfully',
      data: category
    });
  });

  static updateCategory = asyncHandler(async (req: Request, res: Response) => {
    const { id }: CategoryParams = req.params as any;
    const data: UpdateCategoryInput = req.body;
    const category = await CategoryService.updateCategory(id, data);
    
    res.status(200).json({
      success: true,
      message: 'Category updated successfully',
      data: category
    });
  });

  static deleteCategory = asyncHandler(async (req: Request, res: Response) => {
    const { id }: CategoryParams = req.params as any;
    const result = await CategoryService.deleteCategory(id);
    
    res.status(200).json({
      success: true,
      message: result.message
    });
  });
}
