#!/usr/bin/env tsx

/**
 * Migration Test Script
 * 
 * This script tests the migration functionality by:
 * 1. Running a small test migration
 * 2. Validating the results
 * 3. Cleaning up test data
 * 
 * Usage:
 *   npm run migrate:test
 *   or
 *   npx tsx scripts/test-migration.ts
 */

import { PrismaClient } from '@prisma/client';
import { firestoreService } from '../src/services/firestoreService';
import { COLLECTIONS } from '../src/types/firestore';
import type { User, Category } from '../src/types/firestore';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

class MigrationTester {
  private testIds: string[] = [];

  async runTests(): Promise<void> {
    console.log('🧪 Starting migration tests...\n');
    
    try {
      // Test 1: Create test data in SQLite
      await this.createTestData();
      
      // Test 2: Test migration of test data
      await this.testMigration();
      
      // Test 3: Validate migrated data
      await this.validateMigration();
      
      // Test 4: Clean up
      await this.cleanup();
      
      console.log('✅ All migration tests passed!\n');
      
    } catch (error) {
      console.error('❌ Migration tests failed:', error);
      await this.cleanup();
      throw error;
    }
  }

  private async createTestData(): Promise<void> {
    console.log('📝 Creating test data in SQLite...');
    
    try {
      // Create test user
      const hashedPassword = await bcrypt.hash('test123', 12);
      const testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'test-migration',
          password: hashedPassword,
          firstName: 'Test',
          lastName: 'Migration',
          role: 'CASHIER',
          isActive: true
        }
      });
      this.testIds.push(testUser.id);
      
      // Create test category
      const testCategory = await prisma.category.create({
        data: {
          name: 'Test Migration Category',
          description: 'Category created for migration testing',
          isActive: true
        }
      });
      this.testIds.push(testCategory.id);
      
      console.log('  ✅ Test data created in SQLite');
      
    } catch (error) {
      console.error('  ❌ Failed to create test data:', error);
      throw error;
    }
  }

  private async testMigration(): Promise<void> {
    console.log('🔄 Testing migration of test data...');
    
    try {
      // Get test data from SQLite
      const sqliteUsers = await prisma.user.findMany({
        where: { email: '<EMAIL>' }
      });
      
      const sqliteCategories = await prisma.category.findMany({
        where: { name: 'Test Migration Category' }
      });
      
      // Migrate test user
      if (sqliteUsers.length > 0) {
        const sqliteUser = sqliteUsers[0];
        const firestoreUser: User = {
          id: sqliteUser.id,
          email: sqliteUser.email,
          username: sqliteUser.username,
          password: sqliteUser.password,
          firstName: sqliteUser.firstName,
          lastName: sqliteUser.lastName,
          role: sqliteUser.role as User['role'],
          isActive: sqliteUser.isActive,
          createdAt: sqliteUser.createdAt,
          updatedAt: sqliteUser.updatedAt
        };
        
        await firestoreService.createWithId(COLLECTIONS.USERS, sqliteUser.id, firestoreUser);
        console.log('  ✅ Test user migrated to Firestore');
      }
      
      // Migrate test category
      if (sqliteCategories.length > 0) {
        const sqliteCategory = sqliteCategories[0];
        const firestoreCategory: Category = {
          id: sqliteCategory.id,
          name: sqliteCategory.name,
          description: sqliteCategory.description || undefined,
          isActive: sqliteCategory.isActive,
          createdAt: sqliteCategory.createdAt,
          updatedAt: sqliteCategory.updatedAt
        };
        
        await firestoreService.createWithId(COLLECTIONS.CATEGORIES, sqliteCategory.id, firestoreCategory);
        console.log('  ✅ Test category migrated to Firestore');
      }
      
    } catch (error) {
      console.error('  ❌ Failed to migrate test data:', error);
      throw error;
    }
  }

  private async validateMigration(): Promise<void> {
    console.log('🔍 Validating migrated test data...');
    
    try {
      // Validate user migration
      const firestoreUser = await firestoreService.findMany<User>(
        COLLECTIONS.USERS,
        {
          where: [{ field: 'email', operator: '==', value: '<EMAIL>' }],
          limit: 1
        }
      );
      
      if (firestoreUser.length === 0) {
        throw new Error('Test user not found in Firestore');
      }
      
      const user = firestoreUser[0];
      if (user.username !== 'test-migration' || user.firstName !== 'Test') {
        throw new Error('Test user data mismatch in Firestore');
      }
      
      console.log('  ✅ Test user validation passed');
      
      // Validate category migration
      const firestoreCategory = await firestoreService.findMany<Category>(
        COLLECTIONS.CATEGORIES,
        {
          where: [{ field: 'name', operator: '==', value: 'Test Migration Category' }],
          limit: 1
        }
      );
      
      if (firestoreCategory.length === 0) {
        throw new Error('Test category not found in Firestore');
      }
      
      const category = firestoreCategory[0];
      if (category.description !== 'Category created for migration testing') {
        throw new Error('Test category data mismatch in Firestore');
      }
      
      console.log('  ✅ Test category validation passed');
      
    } catch (error) {
      console.error('  ❌ Failed to validate migrated data:', error);
      throw error;
    }
  }

  private async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up test data...');
    
    try {
      // Clean up SQLite test data
      await prisma.user.deleteMany({
        where: { email: '<EMAIL>' }
      });
      
      await prisma.category.deleteMany({
        where: { name: 'Test Migration Category' }
      });
      
      console.log('  ✅ SQLite test data cleaned up');
      
      // Clean up Firestore test data
      const firestoreUsers = await firestoreService.findMany<User>(
        COLLECTIONS.USERS,
        {
          where: [{ field: 'email', operator: '==', value: '<EMAIL>' }],
          limit: 10
        }
      );
      
      for (const user of firestoreUsers) {
        await firestoreService.delete(COLLECTIONS.USERS, user.id);
      }
      
      const firestoreCategories = await firestoreService.findMany<Category>(
        COLLECTIONS.CATEGORIES,
        {
          where: [{ field: 'name', operator: '==', value: 'Test Migration Category' }],
          limit: 10
        }
      );
      
      for (const category of firestoreCategories) {
        await firestoreService.delete(COLLECTIONS.CATEGORIES, category.id);
      }
      
      console.log('  ✅ Firestore test data cleaned up');
      
    } catch (error) {
      console.error('  ⚠️ Cleanup failed (non-critical):', error);
      // Don't throw error for cleanup failures
    }
  }
}

// Main execution
async function main() {
  const tester = new MigrationTester();
  
  try {
    await tester.runTests();
    console.log('🎉 Migration testing completed successfully!');
  } catch (error) {
    console.error('💥 Migration testing failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log('\n👋 Migration test script finished.');
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  main().catch(console.error);
}

export { MigrationTester };
