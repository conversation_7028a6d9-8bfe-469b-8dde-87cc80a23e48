import bcrypt from "bcryptjs";
import { logger } from "../config/logger";
import { CustomError } from "../middleware/errorHandler";
import { CreateUserInput, UpdateUserInput, UserQuery } from "../schemas/user";

export class UserService {
  private static readonly SALT_ROUNDS = 12;

  static async createUser(data: CreateUserInput) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type User = import("../types/firestore").User;

      // Check if user already exists by email
      const existingUserByEmail = await firestoreService.findMany<User>(
        COLLECTIONS.USERS,
        {
          where: [{ field: "email", operator: "==", value: data.email }],
          limit: 1,
        }
      );

      if (existingUserByEmail.length > 0) {
        throw new CustomError("User with this email already exists", 409);
      }

      // Check if user already exists by username
      const existingUserByUsername = await firestoreService.findMany<User>(
        COLLECTIONS.USERS,
        {
          where: [{ field: "username", operator: "==", value: data.username }],
          limit: 1,
        }
      );

      if (existingUserByUsername.length > 0) {
        throw new CustomError("User with this username already exists", 409);
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(data.password, this.SALT_ROUNDS);

      // Create user
      const userData = {
        ...data,
        password: hashedPassword,
        isActive: true,
      };

      const user = await firestoreService.create<User>(
        COLLECTIONS.USERS,
        userData
      );

      logger.info(`User created: ${user.email}`);

      // Return user without password
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      logger.error("User creation failed:", error);
      throw error;
    }
  }

  static async getUsers(query: UserQuery) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type User = import("../types/firestore").User;

      const { page, limit, search, role, isActive } = query;

      // Build where conditions for Firestore
      const whereConditions: Array<{
        field: string;
        operator: any;
        value: any;
      }> = [];

      // Filter by role
      if (role) {
        whereConditions.push({ field: "role", operator: "==", value: role });
      }

      // Filter by active status
      if (isActive !== undefined) {
        whereConditions.push({
          field: "isActive",
          operator: "==",
          value: isActive,
        });
      }

      let users: User[];
      let total: number;

      // Handle search functionality
      if (search) {
        // Simple search on firstName (Firestore limitation)
        const searchResults = await firestoreService.search<User>(
          COLLECTIONS.USERS,
          "firstName",
          search.toLowerCase(),
          {
            limit: 1000,
            additionalWhere: whereConditions,
          }
        );

        // Filter results manually for better search
        const filteredUsers = searchResults.filter((user: User) => {
          const searchLower = search.toLowerCase();
          return (
            user.firstName.toLowerCase().includes(searchLower) ||
            user.lastName.toLowerCase().includes(searchLower) ||
            user.email.toLowerCase().includes(searchLower) ||
            user.username.toLowerCase().includes(searchLower)
          );
        });

        total = filteredUsers.length;
        const skip = (page - 1) * limit;
        users = filteredUsers.slice(skip, skip + limit);
      } else {
        // Use pagination for regular queries
        const result = await firestoreService.findManyWithPagination<User>(
          COLLECTIONS.USERS,
          {
            page,
            limit,
            where: whereConditions,
            orderBy: { field: "createdAt", direction: "desc" },
          }
        );

        users = result.data;
        total = result.pagination.total;
      }

      // Remove passwords from response
      const usersWithoutPasswords = users.map((user) => {
        const { password, ...userWithoutPassword } = user;
        return userWithoutPassword;
      });

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error("Failed to fetch users:", error);
      throw error;
    }
  }

  static async getUserById(id: string) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type User = import("../types/firestore").User;

      const user = await firestoreService.findById<User>(COLLECTIONS.USERS, id);

      if (!user) {
        throw new CustomError("User not found", 404);
      }

      // Return user without password
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      logger.error("Failed to fetch user:", error);
      throw error;
    }
  }

  static async updateUser(id: string, data: UpdateUserInput) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type User = import("../types/firestore").User;

      // Check if user exists
      const existingUser = await firestoreService.findById<User>(
        COLLECTIONS.USERS,
        id
      );

      if (!existingUser) {
        throw new CustomError("User not found", 404);
      }

      // Check for email conflicts
      if (data.email) {
        const conflictUsers = await firestoreService.findMany<User>(
          COLLECTIONS.USERS,
          {
            where: [{ field: "email", operator: "==", value: data.email }],
            limit: 1,
          }
        );

        if (conflictUsers.length > 0 && conflictUsers[0].id !== id) {
          throw new CustomError("Email already exists", 409);
        }
      }

      // Check for username conflicts
      if (data.username) {
        const conflictUsers = await firestoreService.findMany<User>(
          COLLECTIONS.USERS,
          {
            where: [
              { field: "username", operator: "==", value: data.username },
            ],
            limit: 1,
          }
        );

        if (conflictUsers.length > 0 && conflictUsers[0].id !== id) {
          throw new CustomError("Username already exists", 409);
        }
      }

      const user = await firestoreService.update<User>(
        COLLECTIONS.USERS,
        id,
        data
      );

      logger.info(`User updated: ${user.email}`);

      // Return user without password
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      logger.error("User update failed:", error);
      throw error;
    }
  }

  static async deleteUser(id: string) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type User = import("../types/firestore").User;

      const user = await firestoreService.findById<User>(COLLECTIONS.USERS, id);

      if (!user) {
        throw new CustomError("User not found", 404);
      }

      await firestoreService.delete(COLLECTIONS.USERS, id);

      logger.info(`User deleted: ${user.email}`);
      return { message: "User deleted successfully" };
    } catch (error) {
      logger.error("User deletion failed:", error);
      throw error;
    }
  }
}
