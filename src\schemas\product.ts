import { z } from "zod";
import { ProductType } from "../types/firestore";

export const createProductSchema = z.object({
  body: z.object({
    name: z.string().min(1, "Product name is required"),
    description: z.string().optional(),
    price: z.number().positive("Price must be positive"),
    cost: z.number().positive("Cost must be positive").optional(),
    sku: z.string().optional(),
    barcode: z.string().optional(),
    type: z.nativeEnum(ProductType),
    categoryId: z.string().min(1, "Category ID is required"),
    stockQuantity: z
      .number()
      .int()
      .min(0, "Stock quantity cannot be negative")
      .default(0),
    minStockLevel: z
      .number()
      .int()
      .min(0, "Minimum stock level cannot be negative")
      .default(0),
    trackInventory: z.boolean().default(true),
    preparationTime: z
      .number()
      .int()
      .positive("Preparation time must be positive")
      .optional(),
    calories: z.number().int().positive("Calories must be positive").optional(),
    allergens: z.string().optional(),
  }),
});

export const updateProductSchema = z.object({
  body: z.object({
    name: z.string().min(1, "Product name is required").optional(),
    description: z.string().optional(),
    price: z.number().positive("Price must be positive").optional(),
    cost: z.number().positive("Cost must be positive").optional(),
    sku: z.string().optional(),
    barcode: z.string().optional(),
    type: z.nativeEnum(ProductType).optional(),
    categoryId: z.string().min(1, "Category ID is required").optional(),
    stockQuantity: z
      .number()
      .int()
      .min(0, "Stock quantity cannot be negative")
      .optional(),
    minStockLevel: z
      .number()
      .int()
      .min(0, "Minimum stock level cannot be negative")
      .optional(),
    trackInventory: z.boolean().optional(),
    preparationTime: z
      .number()
      .int()
      .positive("Preparation time must be positive")
      .optional(),
    calories: z.number().int().positive("Calories must be positive").optional(),
    allergens: z.string().optional(),
    isActive: z.boolean().optional(),
  }),
});

export const productParamsSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Product ID is required"),
  }),
});

export const productQuerySchema = z.object({
  query: z.object({
    page: z
      .string()
      .optional()
      .default("1")
      .transform(Number)
      .pipe(z.number().min(1)),
    limit: z
      .string()
      .optional()
      .default("10")
      .transform(Number)
      .pipe(z.number().min(1).max(100)),
    search: z.string().optional(),
    categoryId: z.string().min(1, "Category ID is required").optional(),
    type: z.nativeEnum(ProductType).optional(),
    isActive: z
      .string()
      .transform((val) => val === "true")
      .optional(),
    lowStock: z
      .string()
      .transform((val) => val === "true")
      .optional(),
  }),
});

export const updateStockSchema = z.object({
  body: z.object({
    quantity: z.number().int(),
    type: z.enum(["ADD", "SUBTRACT", "SET"]),
    reason: z.string().optional(),
  }),
});

export type CreateProductInput = z.infer<typeof createProductSchema>["body"];
export type UpdateProductInput = z.infer<typeof updateProductSchema>["body"];
export type ProductParams = z.infer<typeof productParamsSchema>["params"];
export type ProductQuery = z.infer<typeof productQuerySchema>["query"];
export type UpdateStockInput = z.infer<typeof updateStockSchema>["body"];
