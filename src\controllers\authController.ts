import { Request, Response } from 'express';
import { AuthService } from '../services/authService';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { LoginInput, RegisterInput, ChangePasswordInput } from '../schemas/auth';

export class AuthController {
  static register = asyncHandler(async (req: Request, res: Response) => {
    const data: RegisterInput = req.body;
    const user = await AuthService.register(data);
    
    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: user
    });
  });

  static login = asyncHandler(async (req: Request, res: Response) => {
    const data: LoginInput = req.body;
    const result = await AuthService.login(data);
    
    res.status(200).json({
      success: true,
      message: 'Login successful',
      data: result
    });
  });

  static changePassword = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { currentPassword, newPassword }: ChangePasswordInput = req.body;
    const userId = req.user!.id;
    
    const result = await AuthService.changePassword(userId, currentPassword, newPassword);
    
    res.status(200).json({
      success: true,
      message: result.message
    });
  });

  static refreshToken = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id;
    const result = await AuthService.refreshToken(userId);
    
    res.status(200).json({
      success: true,
      message: 'Token refreshed successfully',
      data: result
    });
  });

  static getProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    res.status(200).json({
      success: true,
      message: 'Profile retrieved successfully',
      data: req.user
    });
  });
}
