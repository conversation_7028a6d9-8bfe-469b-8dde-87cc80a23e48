{"version": 2, "name": "pos-system", "builds": [{"src": "frontend/package.json", "use": "@vercel/next", "config": {"projectSettings": {"framework": "nextjs"}}}, {"src": "src/server.ts", "use": "@vercel/node", "config": {"includeFiles": ["prisma/**", "src/**"]}}], "routes": [{"src": "/api/(.*)", "dest": "/src/server.ts"}, {"src": "/(.*)", "dest": "/frontend/$1"}], "env": {"NODE_ENV": "production"}, "build": {"env": {"NODE_ENV": "production"}}, "functions": {"src/server.ts": {"maxDuration": 30}}, "regions": ["sin1"], "github": {"silent": true}}