{"version": 2, "name": "pos-system", "builds": [{"src": "src/server.ts", "use": "@vercel/node", "config": {"includeFiles": ["src/**", "cash-e4596.json"], "typescript": true}}], "routes": [{"src": "/api/(.*)", "dest": "/src/server.ts"}, {"src": "/health", "dest": "/src/server.ts"}], "env": {"NODE_ENV": "production", "GOOGLE_CLOUD_PROJECT_ID": "@google_cloud_project_id", "FIRESTORE_PROJECT_ID": "@firestore_project_id", "FIRESTORE_PRIVATE_KEY": "@firestore_private_key", "FIRESTORE_CLIENT_EMAIL": "@firestore_client_email", "JWT_SECRET": "@jwt_secret", "JWT_EXPIRES_IN": "@jwt_expires_in", "LOG_LEVEL": "info", "ENABLE_SECURITY_AUDIT_LOGGING": "true", "MAX_CONCURRENT_SESSIONS": "3", "SESSION_INACTIVITY_TIMEOUT": "30", "SESSION_ABSOLUTE_TIMEOUT": "480"}, "build": {"env": {"NODE_ENV": "production"}}, "functions": {"src/server.ts": {"maxDuration": 30}}, "regions": ["sin1"], "github": {"silent": true}}