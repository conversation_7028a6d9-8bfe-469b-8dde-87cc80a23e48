# Database Configuration
DATABASE_URL="file:./dev.db"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-here-change-this-in-production-minimum-32-characters"
JWT_EXPIRES_IN="24h"

# Server Configuration
PORT=3000
NODE_ENV="development"

# CORS Configuration
FRONTEND_URL="http://localhost:3001"

# Logging
LOG_LEVEL="info"

# Firestore Configuration
GOOGLE_CLOUD_PROJECT_ID=
GOOGLE_APPLICATION_CREDENTIALS="./sarvgfdgsdfg.json"
# OR use individual credentials
FIRESTORE_PROJECT_ID=
FIRESTORE_PRIVATE_KEY=
FIRESTORE_CLIENT_EMAIL=

# Security Configuration
ENABLE_SECURITY_AUDIT_LOGGING="true"
MAX_CONCURRENT_SESSIONS="3"
SESSION_INACTIVITY_TIMEOUT="30"
SESSION_ABSOLUTE_TIMEOUT="480"
