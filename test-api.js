const https = require('http');

// Test function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          resolve({ status: res.statusCode, data: response });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testAPI() {
  console.log('🧪 Testing POS Backend API...\n');

  try {
    // Test health endpoint
    console.log('1. Testing health endpoint...');
    const healthResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/health',
      method: 'GET'
    });
    console.log('✅ Health check:', healthResponse.status, healthResponse.data.status);

    // Test register endpoint
    console.log('\n2. Testing user registration...');
    const registerResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, {
      email: '<EMAIL>',
      username: 'admin',
      password: 'admin123',
      firstName: 'System',
      lastName: 'Administrator',
      role: 'ADMIN'
    });
    console.log('✅ Registration:', registerResponse.status, registerResponse.data.success ? 'Success' : registerResponse.data.message);

    // Test login endpoint
    console.log('\n3. Testing user login...');
    const loginResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    console.log('✅ Login:', loginResponse.status, loginResponse.data.success ? 'Success' : loginResponse.data.message);

    if (loginResponse.data.success) {
      const token = loginResponse.data.data.token;
      console.log('🔑 Token received');

      // Test protected endpoint
      console.log('\n4. Testing protected endpoint...');
      const profileResponse = await makeRequest({
        hostname: 'localhost',
        port: 3000,
        path: '/api/auth/profile',
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('✅ Profile:', profileResponse.status, profileResponse.data.success ? 'Success' : profileResponse.data.message);

      // Test categories endpoint
      console.log('\n5. Testing categories endpoint...');
      const categoriesResponse = await makeRequest({
        hostname: 'localhost',
        port: 3000,
        path: '/api/categories',
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('✅ Categories:', categoriesResponse.status, categoriesResponse.data.success ? 'Success' : categoriesResponse.data.message);
    }

    console.log('\n🎉 API testing completed!');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAPI();
