import { Timestamp } from "firebase-admin/firestore";

// Enums (same as Prisma enums)
export enum UserRole {
  ADMIN = "ADMIN",
  MANAGER = "MANAGER",
  CASHIER = "CASHIER",
}

export enum OrderStatus {
  PENDING = "PENDING",
  PREPARING = "PREPARING",
  READY = "READY",
  COMPLETED = "COMPLETED",
  CANCELLED = "CANCELLED",
}

export enum PaymentMethod {
  CASH = "CASH",
  CARD = "CARD",
  DIGITAL_WALLET = "DIGITAL_WALLET",
  BANK_TRANSFER = "BANK_TRANSFER",
}

export enum PaymentStatus {
  PENDING = "PENDING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
  REFUNDED = "REFUNDED",
}

export enum ProductType {
  FOOD = "FOOD",
  BEVERAGE = "BEVERAGE",
  CONFECTIONERY = "CONFECTIONERY",
  COMBO = "COMBO",
}

// Base interface for all Firestore documents
export interface FirestoreDocument {
  id: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// User document structure
export interface User extends FirestoreDocument {
  email: string;
  username: string;
  password: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  isActive: boolean;
}

// Category document structure
export interface Category extends FirestoreDocument {
  name: string;
  description?: string;
  isActive: boolean;
}

// Product document structure
export interface Product extends FirestoreDocument {
  name: string;
  description?: string;
  price: number;
  cost?: number;
  sku?: string;
  barcode?: string;
  type: ProductType;
  isActive: boolean;
  
  // Inventory fields
  stockQuantity: number;
  minStockLevel: number;
  trackInventory: boolean;
  
  // F&B specific fields
  preparationTime?: number; // in minutes
  calories?: number;
  allergens?: string; // JSON string of allergens
  
  // Category reference
  categoryId: string;
}

// Customer document structure
export interface Customer extends FirestoreDocument {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  dateOfBirth?: Timestamp;
  address?: string;
  city?: string;
  postalCode?: string;
  country?: string;
  isActive: boolean;
  
  // Customer analytics
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: Timestamp;
}

// Order item structure (embedded in Order)
export interface OrderItem {
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  notes?: string;
}

// Payment structure (embedded in Order)
export interface Payment {
  id: string;
  amount: number;
  method: PaymentMethod;
  status: PaymentStatus;
  transactionId?: string;
  notes?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Order document structure
export interface Order extends FirestoreDocument {
  orderNumber: string;
  status: OrderStatus;
  
  // Customer information
  customerId?: string;
  customerName?: string;
  
  // Order details
  items: OrderItem[];
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  
  // Payment information
  payments: Payment[];
  paidAmount: number;
  changeAmount: number;
  
  // Order metadata
  notes?: string;
  tableNumber?: string;
  orderType: "DINE_IN" | "TAKEAWAY" | "DELIVERY";
  
  // Staff information
  createdBy: string; // User ID
  completedAt?: Timestamp;
  cancelledAt?: Timestamp;
  cancelReason?: string;
}

// Collection names constants
export const COLLECTIONS = {
  USERS: "users",
  CATEGORIES: "categories",
  PRODUCTS: "products",
  CUSTOMERS: "customers",
  ORDERS: "orders",
} as const;

// Helper types for creating documents (without id, createdAt, updatedAt)
export type CreateUser = Omit<User, "id" | "createdAt" | "updatedAt">;
export type CreateCategory = Omit<Category, "id" | "createdAt" | "updatedAt">;
export type CreateProduct = Omit<Product, "id" | "createdAt" | "updatedAt">;
export type CreateCustomer = Omit<Customer, "id" | "createdAt" | "updatedAt">;
export type CreateOrder = Omit<Order, "id" | "createdAt" | "updatedAt">;

// Helper types for updating documents (all fields optional except id)
export type UpdateUser = Partial<Omit<User, "id" | "createdAt">> & { id: string };
export type UpdateCategory = Partial<Omit<Category, "id" | "createdAt">> & { id: string };
export type UpdateProduct = Partial<Omit<Product, "id" | "createdAt">> & { id: string };
export type UpdateCustomer = Partial<Omit<Customer, "id" | "createdAt">> & { id: string };
export type UpdateOrder = Partial<Omit<Order, "id" | "createdAt">> & { id: string };

// Query interfaces for filtering and pagination
export interface BaseQuery {
  page?: number;
  limit?: number;
  search?: string;
}

export interface UserQuery extends BaseQuery {
  role?: UserRole;
  isActive?: boolean;
}

export interface ProductQuery extends BaseQuery {
  categoryId?: string;
  type?: ProductType;
  isActive?: boolean;
  lowStock?: boolean;
}

export interface OrderQuery extends BaseQuery {
  status?: OrderStatus;
  customerId?: string;
  createdBy?: string;
  startDate?: string;
  endDate?: string;
  orderType?: "DINE_IN" | "TAKEAWAY" | "DELIVERY";
}

export interface CustomerQuery extends BaseQuery {
  isActive?: boolean;
  city?: string;
  country?: string;
}

export interface CategoryQuery extends BaseQuery {
  isActive?: boolean;
}

// Pagination result interface
export interface PaginationResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
