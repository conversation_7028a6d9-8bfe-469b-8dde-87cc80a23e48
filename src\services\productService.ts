import { logger } from "../config/logger";
import { CustomError } from "../middleware/errorHandler";
import {
  CreateProductInput,
  ProductQuery,
  UpdateProductInput,
  UpdateStockInput,
} from "../schemas/product";

export class ProductService {
  static async createProduct(data: CreateProductInput) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Category = import("../types/firestore").Category;
      type Product = import("../types/firestore").Product;

      // Check if category exists
      const category = await firestoreService.findById<Category>(
        COLLECTIONS.CATEGORIES,
        data.categoryId
      );

      if (!category) {
        throw new CustomError("Category not found", 404);
      }

      if (!category.isActive) {
        throw new CustomError(
          "Cannot create product in inactive category",
          400
        );
      }

      // Check for SKU uniqueness if provided
      if (data.sku) {
        const existingProducts = await firestoreService.findMany<Product>(
          COLLECTIONS.PRODUCTS,
          {
            where: [{ field: "sku", operator: "==", value: data.sku }],
            limit: 1,
          }
        );

        if (existingProducts.length > 0) {
          throw new CustomError("Product with this SKU already exists", 409);
        }
      }

      // Check for barcode uniqueness if provided
      if (data.barcode) {
        const existingProducts = await firestoreService.findMany<Product>(
          COLLECTIONS.PRODUCTS,
          {
            where: [{ field: "barcode", operator: "==", value: data.barcode }],
            limit: 1,
          }
        );

        if (existingProducts.length > 0) {
          throw new CustomError(
            "Product with this barcode already exists",
            409
          );
        }
      }

      // Create product
      const productData = {
        ...data,
        isActive: true,
      };

      const product = await firestoreService.create<Product>(
        COLLECTIONS.PRODUCTS,
        productData
      );

      logger.info(`Product created: ${product.name} (ID: ${product.id})`);

      // Return product with category information
      return {
        ...product,
        category: {
          id: category.id,
          name: category.name,
          isActive: category.isActive,
        },
      };
    } catch (error) {
      logger.error("Product creation failed:", error);
      throw error;
    }
  }

  static async getProducts(query: ProductQuery) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Product = import("../types/firestore").Product;
      type Category = import("../types/firestore").Category;

      const { page, limit, search, categoryId, type, isActive, lowStock } =
        query;

      // Build where conditions for Firestore
      const whereConditions: Array<{
        field: string;
        operator: any;
        value: any;
      }> = [];

      // Filter by category
      if (categoryId) {
        whereConditions.push({
          field: "categoryId",
          operator: "==",
          value: categoryId,
        });
      }

      // Filter by product type
      if (type) {
        whereConditions.push({ field: "type", operator: "==", value: type });
      }

      // Filter by active status
      if (isActive !== undefined) {
        whereConditions.push({
          field: "isActive",
          operator: "==",
          value: isActive,
        });
      }

      let products: Product[];
      let total: number;

      // Handle search functionality
      // Note: Firestore doesn't support full-text search like Prisma
      // For production, consider using Algolia or Elasticsearch for search
      if (search) {
        // Simple prefix search on product name
        const searchResults = await firestoreService.search<Product>(
          COLLECTIONS.PRODUCTS,
          "name",
          search.toLowerCase(),
          {
            limit: 1000, // Get more results for filtering
            additionalWhere: whereConditions,
          }
        );

        // Filter results and apply pagination manually
        const filteredProducts = searchResults.filter((product: Product) => {
          const searchLower = search.toLowerCase();
          return (
            product.name.toLowerCase().includes(searchLower) ||
            (product.description &&
              product.description.toLowerCase().includes(searchLower)) ||
            (product.sku && product.sku.toLowerCase().includes(searchLower)) ||
            (product.barcode &&
              product.barcode.toLowerCase().includes(searchLower))
          );
        });

        total = filteredProducts.length;
        const skip = (page - 1) * limit;
        products = filteredProducts.slice(skip, skip + limit);
      } else if (lowStock) {
        // For low stock, we need to get all products that track inventory
        // and filter them in application layer since Firestore doesn't support
        // complex queries like comparing two fields
        whereConditions.push({
          field: "trackInventory",
          operator: "==",
          value: true,
        });

        const allProducts = await firestoreService.findMany<Product>(
          COLLECTIONS.PRODUCTS,
          {
            where: whereConditions,
            orderBy: { field: "createdAt", direction: "desc" },
          }
        );

        // Filter for low stock in application layer
        const lowStockProducts = allProducts.filter(
          (product: Product) => product.stockQuantity <= product.minStockLevel
        );

        total = lowStockProducts.length;
        const skip = (page - 1) * limit;
        products = lowStockProducts.slice(skip, skip + limit);
      } else {
        // Use pagination for regular queries
        const result = await firestoreService.findManyWithPagination<Product>(
          COLLECTIONS.PRODUCTS,
          {
            page,
            limit,
            where: whereConditions,
            orderBy: { field: "createdAt", direction: "desc" },
          }
        );

        products = result.data;
        total = result.pagination.total;
      }

      return {
        products,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error("Failed to fetch products:", error);
      throw error;
    }
  }

  static async getProductById(id: string) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Product = import("../types/firestore").Product;
      type Category = import("../types/firestore").Category;

      const product = await firestoreService.findById<Product>(
        COLLECTIONS.PRODUCTS,
        id
      );

      if (!product) {
        throw new CustomError("Product not found", 404);
      }

      // Get category information
      const category = await firestoreService.findById<Category>(
        COLLECTIONS.CATEGORIES,
        product.categoryId
      );

      return {
        ...product,
        category: category
          ? {
              id: category.id,
              name: category.name,
              isActive: category.isActive,
            }
          : null,
      };
    } catch (error) {
      logger.error("Failed to fetch product:", error);
      throw error;
    }
  }

  static async updateProduct(id: string, data: UpdateProductInput) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Product = import("../types/firestore").Product;
      type Category = import("../types/firestore").Category;

      const existingProduct = await firestoreService.findById<Product>(
        COLLECTIONS.PRODUCTS,
        id
      );

      if (!existingProduct) {
        throw new CustomError("Product not found", 404);
      }

      // Check category exists if being updated
      if (data.categoryId) {
        const category = await firestoreService.findById<Category>(
          COLLECTIONS.CATEGORIES,
          data.categoryId
        );

        if (!category) {
          throw new CustomError("Category not found", 404);
        }

        if (!category.isActive) {
          throw new CustomError(
            "Cannot assign product to inactive category",
            400
          );
        }
      }

      // Check for SKU conflicts if being updated
      if (data.sku) {
        const conflictProducts = await firestoreService.findMany<Product>(
          COLLECTIONS.PRODUCTS,
          {
            where: [{ field: "sku", operator: "==", value: data.sku }],
            limit: 1,
          }
        );

        if (conflictProducts.length > 0 && conflictProducts[0].id !== id) {
          throw new CustomError("Product with this SKU already exists", 409);
        }
      }

      // Check for barcode conflicts if being updated
      if (data.barcode) {
        const conflictProducts = await firestoreService.findMany<Product>(
          COLLECTIONS.PRODUCTS,
          {
            where: [{ field: "barcode", operator: "==", value: data.barcode }],
            limit: 1,
          }
        );

        if (conflictProducts.length > 0 && conflictProducts[0].id !== id) {
          throw new CustomError(
            "Product with this barcode already exists",
            409
          );
        }
      }

      // Update the product
      const updatedProduct = await firestoreService.update<Product>(
        COLLECTIONS.PRODUCTS,
        id,
        data
      );

      // Get category information if needed
      const category = await firestoreService.findById<Category>(
        COLLECTIONS.CATEGORIES,
        updatedProduct.categoryId
      );

      const productWithCategory = {
        ...updatedProduct,
        category: category
          ? {
              id: category.id,
              name: category.name,
              isActive: category.isActive,
            }
          : null,
      };

      logger.info(
        `Product updated: ${updatedProduct.name} (ID: ${updatedProduct.id})`
      );
      return productWithCategory;
    } catch (error) {
      logger.error("Product update failed:", error);
      throw error;
    }
  }

  static async deleteProduct(id: string) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Product = import("../types/firestore").Product;
      type Order = import("../types/firestore").Order;

      const existingProduct = await firestoreService.findById<Product>(
        COLLECTIONS.PRODUCTS,
        id
      );

      if (!existingProduct) {
        throw new CustomError("Product not found", 404);
      }

      // Check if product has been used in any orders
      // Note: In Firestore, we need to query orders that contain this product
      const ordersWithProduct = await firestoreService.findMany<Order>(
        COLLECTIONS.ORDERS,
        {
          where: [
            {
              field: "items",
              operator: "array-contains-any",
              value: [{ productId: id }],
            },
          ],
          limit: 1,
        }
      );

      if (ordersWithProduct.length > 0) {
        throw new CustomError(
          "Cannot delete product that has been used in orders. Consider deactivating instead.",
          400
        );
      }

      await firestoreService.delete(COLLECTIONS.PRODUCTS, id);

      logger.info(`Product deleted: ${existingProduct.name} (ID: ${id})`);
      return { message: "Product deleted successfully" };
    } catch (error) {
      logger.error("Product deletion failed:", error);
      throw error;
    }
  }

  static async updateStock(id: string, data: UpdateStockInput) {
    try {
      // Import Firestore service and types
      const { firestoreService } = await import("./firestoreService");
      const { COLLECTIONS } = await import("../types/firestore");
      type Product = import("../types/firestore").Product;
      type Category = import("../types/firestore").Category;

      return await firestoreService.runTransaction(async (transaction) => {
        const product = await firestoreService.findById<Product>(
          COLLECTIONS.PRODUCTS,
          id
        );

        if (!product) {
          throw new CustomError("Product not found", 404);
        }

        if (!product.trackInventory) {
          throw new CustomError("This product does not track inventory", 400);
        }

        let newStockQuantity: number;

        switch (data.type) {
          case "ADD":
            newStockQuantity = product.stockQuantity + data.quantity;
            break;
          case "SUBTRACT":
            newStockQuantity = product.stockQuantity - data.quantity;
            if (newStockQuantity < 0) {
              throw new CustomError("Insufficient stock quantity", 400);
            }
            break;
          case "SET":
            newStockQuantity = data.quantity;
            if (newStockQuantity < 0) {
              throw new CustomError("Stock quantity cannot be negative", 400);
            }
            break;
          default:
            throw new CustomError("Invalid stock update type", 400);
        }

        const updatedProduct = await firestoreService.update<Product>(
          COLLECTIONS.PRODUCTS,
          id,
          { stockQuantity: newStockQuantity }
        );

        // Get category information
        const category = await firestoreService.findById<Category>(
          COLLECTIONS.CATEGORIES,
          updatedProduct.categoryId
        );

        const productWithCategory = {
          ...updatedProduct,
          category: category
            ? {
                id: category.id,
                name: category.name,
                isActive: category.isActive,
              }
            : null,
        };

        logger.info(
          `Stock updated for product: ${product.name} (ID: ${id}). ` +
            `${data.type} ${data.quantity}. New stock: ${newStockQuantity}. ` +
            `Reason: ${data.reason || "Not specified"}`
        );

        return productWithCategory;
      });
    } catch (error) {
      logger.error("Stock update failed:", error);
      throw error;
    }
  }
}
