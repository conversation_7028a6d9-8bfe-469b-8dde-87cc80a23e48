"use strict";
/**
 * Test Setup and Configuration
 *
 * This file sets up the testing environment for the POS backend system
 * with Firestore integration testing.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestCleanup = exports.TestUtils = exports.TEST_CONFIG = void 0;
exports.isTestEnvironment = isTestEnvironment;
exports.requireTestEnvironment = requireTestEnvironment;
const dotenv_1 = __importDefault(require("dotenv"));
// Load test environment variables
dotenv_1.default.config({ path: '.env.test' });
// Test configuration
exports.TEST_CONFIG = {
    baseURL: process.env.TEST_BASE_URL || 'http://localhost:3000',
    timeout: 30000,
    retries: 3,
    // Test user credentials
    testUsers: {
        admin: {
            email: '<EMAIL>',
            password: 'TestAdmin123!',
            role: 'ADMIN'
        },
        manager: {
            email: '<EMAIL>',
            password: 'TestManager123!',
            role: 'MANAGER'
        },
        cashier: {
            email: '<EMAIL>',
            password: 'TestCashier123!',
            role: 'CASHIER'
        }
    },
    // Test data
    testData: {
        category: {
            name: 'Test Category',
            description: 'Category for testing purposes',
            isActive: true
        },
        product: {
            name: 'Test Product',
            description: 'Product for testing purposes',
            price: 10.99,
            cost: 5.50,
            sku: 'TEST-001',
            type: 'FOOD',
            isActive: true,
            stockQuantity: 100,
            minStockLevel: 10,
            trackInventory: true
        },
        customer: {
            firstName: 'Test',
            lastName: 'Customer',
            email: '<EMAIL>',
            phone: '+1234567890',
            isActive: true
        }
    }
};
// Test utilities
class TestUtils {
    static generateRandomString(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    static generateTestEmail() {
        return `test-${this.generateRandomString()}@example.com`;
    }
    static generateTestUser(role = 'CASHIER') {
        const id = this.generateRandomString();
        return {
            email: `test-user-${id}@example.com`,
            username: `testuser${id}`,
            password: 'TestPassword123!',
            firstName: 'Test',
            lastName: 'User',
            role,
            isActive: true
        };
    }
    static generateTestProduct(categoryId) {
        const id = this.generateRandomString();
        return {
            name: `Test Product ${id}`,
            description: `Test product description ${id}`,
            price: Math.round((Math.random() * 100 + 1) * 100) / 100,
            cost: Math.round((Math.random() * 50 + 1) * 100) / 100,
            sku: `TEST-${id}`,
            type: 'FOOD',
            isActive: true,
            stockQuantity: Math.floor(Math.random() * 100) + 1,
            minStockLevel: Math.floor(Math.random() * 10) + 1,
            trackInventory: true,
            categoryId: categoryId || undefined
        };
    }
    static generateTestCategory() {
        const id = this.generateRandomString();
        return {
            name: `Test Category ${id}`,
            description: `Test category description ${id}`,
            isActive: true
        };
    }
    static generateTestCustomer() {
        const id = this.generateRandomString();
        return {
            firstName: `TestFirst${id}`,
            lastName: `TestLast${id}`,
            email: `customer-${id}@example.com`,
            phone: `+1${Math.floor(Math.random() * 9000000000) + 1000000000}`,
            isActive: true
        };
    }
    static generateTestOrder(customerId, userId) {
        const orderNumber = `TEST-${Date.now()}-${this.generateRandomString(4)}`;
        const subtotal = Math.round((Math.random() * 100 + 10) * 100) / 100;
        const taxAmount = Math.round(subtotal * 0.1 * 100) / 100;
        const discountAmount = 0;
        const totalAmount = subtotal + taxAmount - discountAmount;
        return {
            orderNumber,
            status: 'PENDING',
            subtotal,
            taxAmount,
            discountAmount,
            totalAmount,
            customerId,
            userId,
            orderDate: new Date(),
            orderItems: [],
            payments: []
        };
    }
    static async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    static validateResponse(response, expectedStatus = 200) {
        if (response.status !== expectedStatus) {
            throw new Error(`Expected status ${expectedStatus}, got ${response.status}: ${JSON.stringify(response.data)}`);
        }
        return response.data;
    }
    static validateSuccessResponse(data) {
        if (!data.success) {
            throw new Error(`Expected success response, got: ${JSON.stringify(data)}`);
        }
        return data;
    }
    static validateErrorResponse(data, expectedMessage) {
        if (data.success !== false) {
            throw new Error(`Expected error response, got: ${JSON.stringify(data)}`);
        }
        if (expectedMessage && !data.message.includes(expectedMessage)) {
            throw new Error(`Expected error message to contain "${expectedMessage}", got: ${data.message}`);
        }
        return data;
    }
}
exports.TestUtils = TestUtils;
// Test cleanup utilities
class TestCleanup {
    static trackResource(type, id) {
        this.createdResources[type].push(id);
    }
    static async cleanup() {
        // Note: In a real test environment, you would implement cleanup
        // For now, we'll just reset the tracking
        this.createdResources = {
            users: [],
            categories: [],
            products: [],
            customers: [],
            orders: []
        };
    }
    static getCreatedResources() {
        return { ...this.createdResources };
    }
}
exports.TestCleanup = TestCleanup;
TestCleanup.createdResources = {
    users: [],
    categories: [],
    products: [],
    customers: [],
    orders: []
};
// Export test environment check
function isTestEnvironment() {
    return process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'testing';
}
// Ensure we're in test environment for destructive operations
function requireTestEnvironment() {
    if (!isTestEnvironment()) {
        throw new Error('This operation can only be performed in test environment');
    }
}
