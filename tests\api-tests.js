#!/usr/bin/env tsx
"use strict";
/**
 * Comprehensive API Testing Suite
 *
 * This script tests all API endpoints to ensure the Firestore migration
 * maintains full functionality and compatibility.
 *
 * Usage:
 *   npm run test:api
 *   or
 *   npx tsx tests/api-tests.ts
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.APITester = void 0;
const axios_1 = __importDefault(require("axios"));
const setup_1 = require("./setup");
class APITester {
    constructor() {
        this.authTokens = {};
        this.testResults = [];
        this.client = axios_1.default.create({
            baseURL: setup_1.TEST_CONFIG.baseURL,
            timeout: setup_1.TEST_CONFIG.timeout,
            validateStatus: () => true, // Don't throw on HTTP errors
        });
    }
    async runAllTests() {
        console.log("🧪 Starting Comprehensive API Testing Suite\n");
        console.log(`📍 Base URL: ${setup_1.TEST_CONFIG.baseURL}`);
        console.log(`⏱️  Timeout: ${setup_1.TEST_CONFIG.timeout}ms\n`);
        try {
            // Test suites in order
            await this.testHealthEndpoint();
            await this.testAuthenticationEndpoints();
            await this.testCategoryEndpoints();
            await this.testProductEndpoints();
            await this.testCustomerEndpoints();
            await this.testUserEndpoints();
            await this.testOrderEndpoints();
            await this.testSecurityFeatures();
            this.printSummary();
        }
        catch (error) {
            console.error("💥 Test suite failed:", error);
            throw error;
        }
        finally {
            await setup_1.TestCleanup.cleanup();
        }
    }
    async testHealthEndpoint() {
        const suite = this.createTestSuite("Health Check");
        await this.runTest(suite, "GET /health", async () => {
            const response = await this.client.get("/health");
            setup_1.TestUtils.validateResponse(response, 200);
            const data = response.data;
            if (!data.status || data.status !== "OK") {
                throw new Error("Health check failed");
            }
            return { status: data.status, uptime: data.uptime };
        });
        this.testResults.push(suite);
    }
    async testAuthenticationEndpoints() {
        const suite = this.createTestSuite("Authentication");
        // Test user registration (admin only)
        await this.runTest(suite, "POST /api/auth/register", async () => {
            const testUser = setup_1.TestUtils.generateTestUser("ADMIN");
            const response = await this.client.post("/api/auth/register", testUser);
            setup_1.TestUtils.validateResponse(response, 201);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            setup_1.TestCleanup.trackResource("users", data.data.user.id);
            return data.data.user;
        });
        // Test user login
        await this.runTest(suite, "POST /api/auth/login", async () => {
            const loginData = {
                email: setup_1.TEST_CONFIG.testUsers.admin.email,
                password: setup_1.TEST_CONFIG.testUsers.admin.password,
            };
            const response = await this.client.post("/api/auth/login", loginData);
            setup_1.TestUtils.validateResponse(response, 200);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            this.authTokens.admin = data.data.token;
            return { token: data.data.token, user: data.data.user };
        });
        // Test protected route access
        await this.runTest(suite, "GET /api/auth/me", async () => {
            const response = await this.client.get("/api/auth/me", {
                headers: { Authorization: `Bearer ${this.authTokens.admin}` },
            });
            setup_1.TestUtils.validateResponse(response, 200);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            return data.data;
        });
        // Test invalid login
        await this.runTest(suite, "POST /api/auth/login (invalid)", async () => {
            const response = await this.client.post("/api/auth/login", {
                email: "<EMAIL>",
                password: "wrongpassword",
            });
            setup_1.TestUtils.validateResponse(response, 401);
            setup_1.TestUtils.validateErrorResponse(response.data, "Invalid credentials");
            return response.data;
        });
        this.testResults.push(suite);
    }
    async testCategoryEndpoints() {
        const suite = this.createTestSuite("Categories");
        let categoryId;
        // Create category
        await this.runTest(suite, "POST /api/categories", async () => {
            const testCategory = setup_1.TestUtils.generateTestCategory();
            const response = await this.client.post("/api/categories", testCategory, {
                headers: { Authorization: `Bearer ${this.authTokens.admin}` },
            });
            setup_1.TestUtils.validateResponse(response, 201);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            categoryId = data.data.id;
            setup_1.TestCleanup.trackResource("categories", categoryId);
            return data.data;
        });
        // Get all categories
        await this.runTest(suite, "GET /api/categories", async () => {
            const response = await this.client.get("/api/categories", {
                headers: { Authorization: `Bearer ${this.authTokens.admin}` },
            });
            setup_1.TestUtils.validateResponse(response, 200);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            return { count: data.data.length, pagination: data.pagination };
        });
        // Get category by ID
        await this.runTest(suite, "GET /api/categories/:id", async () => {
            const response = await this.client.get(`/api/categories/${categoryId}`, {
                headers: { Authorization: `Bearer ${this.authTokens.admin}` },
            });
            setup_1.TestUtils.validateResponse(response, 200);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            return data.data;
        });
        // Update category
        await this.runTest(suite, "PUT /api/categories/:id", async () => {
            const updateData = { name: "Updated Test Category" };
            const response = await this.client.put(`/api/categories/${categoryId}`, updateData, {
                headers: { Authorization: `Bearer ${this.authTokens.admin}` },
            });
            setup_1.TestUtils.validateResponse(response, 200);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            return data.data;
        });
        this.testResults.push(suite);
    }
    async testProductEndpoints() {
        const suite = this.createTestSuite("Products");
        let productId;
        // Create product
        await this.runTest(suite, "POST /api/products", async () => {
            const testProduct = setup_1.TestUtils.generateTestProduct();
            const response = await this.client.post("/api/products", testProduct, {
                headers: { Authorization: `Bearer ${this.authTokens.admin}` },
            });
            setup_1.TestUtils.validateResponse(response, 201);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            productId = data.data.id;
            setup_1.TestCleanup.trackResource("products", productId);
            return data.data;
        });
        // Get all products
        await this.runTest(suite, "GET /api/products", async () => {
            const response = await this.client.get("/api/products", {
                headers: { Authorization: `Bearer ${this.authTokens.admin}` },
            });
            setup_1.TestUtils.validateResponse(response, 200);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            return { count: data.data.length, pagination: data.pagination };
        });
        // Search products
        await this.runTest(suite, "GET /api/products?search=test", async () => {
            const response = await this.client.get("/api/products?search=test", {
                headers: { Authorization: `Bearer ${this.authTokens.admin}` },
            });
            setup_1.TestUtils.validateResponse(response, 200);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            return { count: data.data.length };
        });
        // Update product
        await this.runTest(suite, "PUT /api/products/:id", async () => {
            const updateData = { price: 15.99 };
            const response = await this.client.put(`/api/products/${productId}`, updateData, {
                headers: { Authorization: `Bearer ${this.authTokens.admin}` },
            });
            setup_1.TestUtils.validateResponse(response, 200);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            return data.data;
        });
        this.testResults.push(suite);
    }
    async testCustomerEndpoints() {
        const suite = this.createTestSuite("Customers");
        let customerId;
        // Create customer
        await this.runTest(suite, "POST /api/customers", async () => {
            const testCustomer = setup_1.TestUtils.generateTestCustomer();
            const response = await this.client.post("/api/customers", testCustomer, {
                headers: { Authorization: `Bearer ${this.authTokens.admin}` },
            });
            setup_1.TestUtils.validateResponse(response, 201);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            customerId = data.data.id;
            setup_1.TestCleanup.trackResource("customers", customerId);
            return data.data;
        });
        // Get all customers
        await this.runTest(suite, "GET /api/customers", async () => {
            const response = await this.client.get("/api/customers", {
                headers: { Authorization: `Bearer ${this.authTokens.admin}` },
            });
            setup_1.TestUtils.validateResponse(response, 200);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            return { count: data.data.length };
        });
        this.testResults.push(suite);
    }
    async testUserEndpoints() {
        const suite = this.createTestSuite("Users");
        // Get all users (admin only)
        await this.runTest(suite, "GET /api/users", async () => {
            const response = await this.client.get("/api/users", {
                headers: { Authorization: `Bearer ${this.authTokens.admin}` },
            });
            setup_1.TestUtils.validateResponse(response, 200);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            return { count: data.data.length };
        });
        this.testResults.push(suite);
    }
    async testOrderEndpoints() {
        const suite = this.createTestSuite("Orders");
        // Get all orders
        await this.runTest(suite, "GET /api/orders", async () => {
            const response = await this.client.get("/api/orders", {
                headers: { Authorization: `Bearer ${this.authTokens.admin}` },
            });
            setup_1.TestUtils.validateResponse(response, 200);
            const data = setup_1.TestUtils.validateSuccessResponse(response.data);
            return { count: data.data.length };
        });
        this.testResults.push(suite);
    }
    async testSecurityFeatures() {
        const suite = this.createTestSuite("Security");
        // Test unauthorized access
        await this.runTest(suite, "Unauthorized access protection", async () => {
            const response = await this.client.get("/api/users");
            setup_1.TestUtils.validateResponse(response, 401);
            setup_1.TestUtils.validateErrorResponse(response.data, "Access token required");
            return response.data;
        });
        // Test invalid token
        await this.runTest(suite, "Invalid token protection", async () => {
            const response = await this.client.get("/api/users", {
                headers: { Authorization: "Bearer invalid-token" },
            });
            setup_1.TestUtils.validateResponse(response, 401);
            setup_1.TestUtils.validateErrorResponse(response.data, "Invalid token");
            return response.data;
        });
        // Test rate limiting (if enabled)
        await this.runTest(suite, "Rate limiting", async () => {
            // Make multiple rapid requests to test rate limiting
            const promises = Array(10)
                .fill(0)
                .map(() => this.client.get("/health"));
            const responses = await Promise.all(promises);
            const successCount = responses.filter((r) => r.status === 200).length;
            return { totalRequests: 10, successfulRequests: successCount };
        });
        this.testResults.push(suite);
    }
    createTestSuite(name) {
        return {
            name,
            results: [],
            totalTests: 0,
            passedTests: 0,
            failedTests: 0,
            skippedTests: 0,
            duration: 0,
        };
    }
    async runTest(suite, testName, testFn) {
        const startTime = Date.now();
        suite.totalTests++;
        try {
            console.log(`  🧪 ${testName}...`);
            const result = await testFn();
            const duration = Date.now() - startTime;
            suite.results.push({
                name: testName,
                status: "PASS",
                duration,
                details: result,
            });
            suite.passedTests++;
            suite.duration += duration;
            console.log(`    ✅ PASS (${duration}ms)`);
        }
        catch (error) {
            const duration = Date.now() - startTime;
            suite.results.push({
                name: testName,
                status: "FAIL",
                duration,
                error: error.message,
            });
            suite.failedTests++;
            suite.duration += duration;
            console.log(`    ❌ FAIL (${duration}ms): ${error.message}`);
        }
    }
    printSummary() {
        console.log("\n📊 Test Results Summary");
        console.log("=".repeat(60));
        let totalTests = 0;
        let totalPassed = 0;
        let totalFailed = 0;
        let totalDuration = 0;
        this.testResults.forEach((suite) => {
            const passRate = suite.totalTests > 0
                ? ((suite.passedTests / suite.totalTests) * 100).toFixed(1)
                : "0.0";
            console.log(`\n📋 ${suite.name}`);
            console.log(`   Tests: ${suite.totalTests} | Passed: ${suite.passedTests} | Failed: ${suite.failedTests} | Pass Rate: ${passRate}%`);
            console.log(`   Duration: ${suite.duration}ms`);
            if (suite.failedTests > 0) {
                console.log("   Failed Tests:");
                suite.results
                    .filter((r) => r.status === "FAIL")
                    .forEach((result) => {
                    console.log(`     ❌ ${result.name}: ${result.error}`);
                });
            }
            totalTests += suite.totalTests;
            totalPassed += suite.passedTests;
            totalFailed += suite.failedTests;
            totalDuration += suite.duration;
        });
        const overallPassRate = totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(1) : "0.0";
        console.log("\n" + "=".repeat(60));
        console.log(`🎯 Overall Results:`);
        console.log(`   Total Tests: ${totalTests}`);
        console.log(`   Passed: ${totalPassed}`);
        console.log(`   Failed: ${totalFailed}`);
        console.log(`   Pass Rate: ${overallPassRate}%`);
        console.log(`   Total Duration: ${totalDuration}ms`);
        if (totalFailed === 0) {
            console.log("\n🎉 All tests passed! The Firestore migration is successful.");
        }
        else {
            console.log(`\n⚠️  ${totalFailed} test(s) failed. Please review the failures above.`);
        }
    }
}
exports.APITester = APITester;
// Main execution
async function main() {
    const tester = new APITester();
    try {
        await tester.runAllTests();
    }
    catch (error) {
        console.error("💥 Test execution failed:", error);
        process.exit(1);
    }
}
// Run tests if this script is executed directly
if (require.main === module) {
    main().catch(console.error);
}
