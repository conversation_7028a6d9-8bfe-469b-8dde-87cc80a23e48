import { Request, Response } from 'express';
import { OrderService } from '../services/orderService';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { 
  CreateOrderInput, 
  UpdateOrderInput, 
  OrderParams, 
  OrderQuery,
  CreatePaymentInput,
  UpdatePaymentInput,
  PaymentParams
} from '../schemas/order';

export class OrderController {
  static createOrder = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const data: CreateOrderInput = req.body;
    const userId = req.user!.id;
    const order = await OrderService.createOrder(data, userId);
    
    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      data: order
    });
  });

  static getOrders = asyncHandler(async (req: Request, res: Response) => {
    const query: OrderQuery = req.query as any;
    const result = await OrderService.getOrders(query);
    
    res.status(200).json({
      success: true,
      message: 'Orders retrieved successfully',
      data: result.orders,
      pagination: result.pagination
    });
  });

  static getOrderById = asyncHandler(async (req: Request, res: Response) => {
    const { id }: OrderParams = req.params as any;
    const order = await OrderService.getOrderById(id);
    
    res.status(200).json({
      success: true,
      message: 'Order retrieved successfully',
      data: order
    });
  });

  static updateOrder = asyncHandler(async (req: Request, res: Response) => {
    const { id }: OrderParams = req.params as any;
    const data: UpdateOrderInput = req.body;
    const order = await OrderService.updateOrder(id, data);
    
    res.status(200).json({
      success: true,
      message: 'Order updated successfully',
      data: order
    });
  });

  static createPayment = asyncHandler(async (req: Request, res: Response) => {
    const { orderId } = req.params;
    const data: CreatePaymentInput = req.body;
    const payment = await OrderService.createPayment(orderId, data);
    
    res.status(201).json({
      success: true,
      message: 'Payment created successfully',
      data: payment
    });
  });

  static updatePayment = asyncHandler(async (req: Request, res: Response) => {
    const { orderId, paymentId }: PaymentParams = req.params as any;
    const data: UpdatePaymentInput = req.body;
    const payment = await OrderService.updatePayment(orderId, paymentId, data);
    
    res.status(200).json({
      success: true,
      message: 'Payment updated successfully',
      data: payment
    });
  });
}
