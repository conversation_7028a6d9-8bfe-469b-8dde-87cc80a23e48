/**
 * Enhanced Security Configuration for Firestore POS Backend
 *
 * This module provides comprehensive security configurations including:
 * - JWT security settings
 * - Password policies
 * - Session management
 * - Security headers
 * - Audit logging
 * - Environment validation
 */

import { logger } from "./logger";

export interface SecurityConfig {
  jwt: {
    secret: string;
    expiresIn: string;
    algorithm: string;
    issuer: string;
    audience: string;
  };
  password: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    saltRounds: number;
    maxAge: number; // in days
  };
  session: {
    maxConcurrentSessions: number;
    inactivityTimeout: number; // in minutes
    absoluteTimeout: number; // in minutes
  };
  rateLimit: {
    windowMs: number;
    maxRequests: number;
    authMaxRequests: number;
    createMaxRequests: number;
  };
  audit: {
    logFailedLogins: boolean;
    logSuccessfulLogins: boolean;
    logDataChanges: boolean;
    logAdminActions: boolean;
    retentionDays: number;
  };
  firestore: {
    enableAuditLogging: boolean;
    maxBatchSize: number;
    queryTimeout: number;
    maxRetries: number;
  };
}

// Get security configuration (called after environment variables are loaded)
function getSecurityConfig(): SecurityConfig {
  return {
    jwt: {
      secret: process.env.JWT_SECRET || "",
      expiresIn: process.env.JWT_EXPIRES_IN || "24h",
      algorithm: "HS256",
      issuer: "pos-backend",
      audience: "pos-frontend",
    },
    password: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      saltRounds: 12,
      maxAge: 90, // 90 days
    },
    session: {
      maxConcurrentSessions: 3,
      inactivityTimeout: 30, // 30 minutes
      absoluteTimeout: 480, // 8 hours
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 100,
      authMaxRequests: 5,
      createMaxRequests: 10,
    },
    audit: {
      logFailedLogins: true,
      logSuccessfulLogins: true,
      logDataChanges: true,
      logAdminActions: true,
      retentionDays: 90,
    },
    firestore: {
      enableAuditLogging: true,
      maxBatchSize: 500,
      queryTimeout: 30000, // 30 seconds
      maxRetries: 3,
    },
  };
}

// Validate security configuration
export function validateSecurityConfig(): void {
  const errors: string[] = [];
  const config = getSecurityConfig();

  // Validate JWT secret
  if (!config.jwt.secret) {
    errors.push("JWT_SECRET environment variable is required");
  } else if (config.jwt.secret.length < 32) {
    errors.push("JWT_SECRET must be at least 32 characters long");
  }

  // Validate environment
  if (process.env.NODE_ENV === "production") {
    if (
      config.jwt.secret ===
      "your-super-secret-jwt-key-here-change-this-in-production"
    ) {
      errors.push("Default JWT_SECRET detected in production environment");
    }

    if (
      !process.env.GOOGLE_APPLICATION_CREDENTIALS &&
      !process.env.FIRESTORE_PRIVATE_KEY
    ) {
      errors.push("Firestore credentials not configured for production");
    }
  }

  if (errors.length > 0) {
    logger.error("Security configuration validation failed:", { errors });
    throw new Error(`Security configuration errors: ${errors.join(", ")}`);
  }

  logger.info("Security configuration validated successfully");
}

// Password validation function
export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  const config = getSecurityConfig().password;

  if (password.length < config.minLength) {
    errors.push(
      `Password must be at least ${config.minLength} characters long`
    );
  }

  if (config.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push("Password must contain at least one uppercase letter");
  }

  if (config.requireLowercase && !/[a-z]/.test(password)) {
    errors.push("Password must contain at least one lowercase letter");
  }

  if (config.requireNumbers && !/\d/.test(password)) {
    errors.push("Password must contain at least one number");
  }

  if (
    config.requireSpecialChars &&
    !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
  ) {
    errors.push("Password must contain at least one special character");
  }

  // Check for common weak passwords
  const commonPasswords = [
    "password",
    "123456",
    "123456789",
    "qwerty",
    "abc123",
    "password123",
    "admin",
    "letmein",
    "welcome",
    "monkey",
  ];

  if (commonPasswords.includes(password.toLowerCase())) {
    errors.push("Password is too common and easily guessable");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Security headers configuration
export const securityHeaders = {
  "X-Content-Type-Options": "nosniff",
  "X-Frame-Options": "DENY",
  "X-XSS-Protection": "1; mode=block",
  "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
  "Referrer-Policy": "strict-origin-when-cross-origin",
  "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
  "Content-Security-Policy": [
    "default-src 'self'",
    "script-src 'self'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "connect-src 'self'",
    "font-src 'self'",
    "object-src 'none'",
    "media-src 'self'",
    "frame-src 'none'",
  ].join("; "),
};

// Audit logging function
export function logSecurityEvent(
  event: string,
  userId?: string,
  details?: Record<string, any>,
  severity: "info" | "warn" | "error" = "info"
): void {
  const auditLog = {
    timestamp: new Date().toISOString(),
    event,
    userId,
    details,
    severity,
    source: "security-audit",
  };

  switch (severity) {
    case "error":
      logger.error(`Security Event: ${event}`, auditLog);
      break;
    case "warn":
      logger.warn(`Security Event: ${event}`, auditLog);
      break;
    default:
      logger.info(`Security Event: ${event}`, auditLog);
  }
}

// IP address validation
export function isValidIP(ip: string): boolean {
  const ipv4Regex =
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

  return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

// Generate secure random token
export function generateSecureToken(length: number = 32): string {
  const chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";

  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return result;
}

// Input sanitization
export function sanitizeInput(input: string): string {
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "")
    .replace(/javascript:/gi, "")
    .replace(/on\w+\s*=/gi, "")
    .replace(/[<>]/g, "")
    .trim();
}

// Validate user role
export function isValidUserRole(role: string): boolean {
  const validRoles = ["ADMIN", "MANAGER", "CASHIER"];
  return validRoles.includes(role);
}

// Check if user has required permissions
export function hasPermission(
  userRole: string,
  requiredRoles: string[]
): boolean {
  return requiredRoles.includes(userRole);
}

// Role hierarchy for permission checking
const roleHierarchy: Record<string, number> = {
  CASHIER: 1,
  MANAGER: 2,
  ADMIN: 3,
};

export function hasMinimumRole(userRole: string, minimumRole: string): boolean {
  return (roleHierarchy[userRole] || 0) >= (roleHierarchy[minimumRole] || 0);
}

// Export the security configuration
export const securityConfig = getSecurityConfig();

// Initialize security configuration
export function initializeSecurity(): void {
  try {
    validateSecurityConfig();
    const config = getSecurityConfig();
    logSecurityEvent("security_config_initialized", undefined, {
      environment: process.env.NODE_ENV,
      jwtExpiresIn: config.jwt.expiresIn,
      passwordPolicy: {
        minLength: config.password.minLength,
        requireComplexity: true,
      },
    });
  } catch (error) {
    logger.error("Failed to initialize security configuration:", error);
    throw error;
  }
}
