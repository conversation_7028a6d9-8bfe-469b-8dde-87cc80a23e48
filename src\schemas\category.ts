import { z } from "zod";

export const createCategorySchema = z.object({
  body: z.object({
    name: z.string().min(1, "Category name is required"),
    description: z.string().optional(),
  }),
});

export const updateCategorySchema = z.object({
  body: z.object({
    name: z.string().min(1, "Category name is required").optional(),
    description: z.string().optional(),
    isActive: z.boolean().optional(),
  }),
});

export const categoryParamsSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Category ID is required"),
  }),
});

export const categoryQuerySchema = z.object({
  query: z.object({
    page: z
      .string()
      .optional()
      .default("1")
      .transform(Number)
      .pipe(z.number().min(1)),
    limit: z
      .string()
      .optional()
      .default("10")
      .transform(Number)
      .pipe(z.number().min(1).max(100)),
    search: z.string().optional(),
    isActive: z
      .string()
      .transform((val) => val === "true")
      .optional(),
  }),
});

export type CreateCategoryInput = z.infer<typeof createCategorySchema>["body"];
export type UpdateCategoryInput = z.infer<typeof updateCategorySchema>["body"];
export type CategoryParams = z.infer<typeof categoryParamsSchema>["params"];
export type CategoryQuery = z.infer<typeof categoryQuerySchema>["query"];
