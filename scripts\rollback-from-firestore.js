#!/usr/bin/env tsx
"use strict";
/**
 * Data Rollback Script: Firestore to SQLite (Prisma)
 *
 * This script migrates data back from Firestore to SQLite database
 * in case a rollback is needed after migration.
 *
 * Usage:
 *   npm run migrate:rollback
 *   or
 *   npx tsx scripts/rollback-from-firestore.ts
 *
 * Features:
 * - Migrates all entities back to SQLite
 * - Recreates relational structure from embedded documents
 * - Provides progress tracking and error handling
 * - Validates rollback success
 *
 * WARNING: This will clear existing SQLite data before rollback!
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataRollback = void 0;
const client_1 = require("@prisma/client");
const firestoreService_1 = require("../src/services/firestoreService");
const firestore_1 = require("../src/types/firestore");
const prisma = new client_1.PrismaClient();
class DataRollback {
    constructor() {
        this.stats = {
            users: 0,
            categories: 0,
            products: 0,
            customers: 0,
            orders: 0,
            orderItems: 0,
            payments: 0,
            errors: [],
        };
    }
    async rollback() {
        console.log("🔄 Starting data rollback from Firestore to SQLite...\n");
        try {
            // Step 1: Clear existing SQLite data
            await this.clearSQLiteData();
            // Step 2: Rollback Users
            await this.rollbackUsers();
            // Step 3: Rollback Categories
            await this.rollbackCategories();
            // Step 4: Rollback Products
            await this.rollbackProducts();
            // Step 5: Rollback Customers
            await this.rollbackCustomers();
            // Step 6: Rollback Orders (with OrderItems and Payments)
            await this.rollbackOrders();
            // Step 7: Validate rollback
            await this.validateRollback();
            this.printSummary();
        }
        catch (error) {
            console.error("❌ Rollback failed:", error);
            this.stats.errors.push(`Rollback failed: ${error.message}`);
            throw error;
        }
    }
    async clearSQLiteData() {
        console.log("🗑️ Clearing existing SQLite data...");
        try {
            // Delete in reverse order to respect foreign key constraints
            await prisma.payment.deleteMany();
            await prisma.orderItem.deleteMany();
            await prisma.order.deleteMany();
            await prisma.product.deleteMany();
            await prisma.category.deleteMany();
            await prisma.customer.deleteMany();
            await prisma.user.deleteMany();
            console.log("✅ SQLite data cleared\n");
        }
        catch (error) {
            console.error("❌ Failed to clear SQLite data:", error);
            throw error;
        }
    }
    async rollbackUsers() {
        console.log("👥 Rolling back Users...");
        const firestoreUsers = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.USERS, { limit: 1000 });
        for (const firestoreUser of firestoreUsers) {
            try {
                await prisma.user.create({
                    data: {
                        id: firestoreUser.id,
                        email: firestoreUser.email,
                        username: firestoreUser.username,
                        password: firestoreUser.password,
                        firstName: firestoreUser.firstName,
                        lastName: firestoreUser.lastName,
                        role: firestoreUser.role,
                        isActive: firestoreUser.isActive,
                        createdAt: firestoreUser.createdAt,
                        updatedAt: firestoreUser.updatedAt,
                    },
                });
                this.stats.users++;
                console.log(`  ✅ Rolled back user: ${firestoreUser.email}`);
            }
            catch (error) {
                const errorMsg = `Failed to rollback user ${firestoreUser.email}: ${error.message}`;
                console.error(`  ❌ ${errorMsg}`);
                this.stats.errors.push(errorMsg);
            }
        }
        console.log(`✅ Users rollback completed: ${this.stats.users} users rolled back\n`);
    }
    async rollbackCategories() {
        console.log("📂 Rolling back Categories...");
        const firestoreCategories = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.CATEGORIES, { limit: 1000 });
        for (const firestoreCategory of firestoreCategories) {
            try {
                await prisma.category.create({
                    data: {
                        id: firestoreCategory.id,
                        name: firestoreCategory.name,
                        description: firestoreCategory.description || null,
                        isActive: firestoreCategory.isActive,
                        createdAt: firestoreCategory.createdAt,
                        updatedAt: firestoreCategory.updatedAt,
                    },
                });
                this.stats.categories++;
                console.log(`  ✅ Rolled back category: ${firestoreCategory.name}`);
            }
            catch (error) {
                const errorMsg = `Failed to rollback category ${firestoreCategory.name}: ${error.message}`;
                console.error(`  ❌ ${errorMsg}`);
                this.stats.errors.push(errorMsg);
            }
        }
        console.log(`✅ Categories rollback completed: ${this.stats.categories} categories rolled back\n`);
    }
    async rollbackProducts() {
        console.log("🛍️ Rolling back Products...");
        const firestoreProducts = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.PRODUCTS, { limit: 1000 });
        for (const firestoreProduct of firestoreProducts) {
            try {
                await prisma.product.create({
                    data: {
                        id: firestoreProduct.id,
                        name: firestoreProduct.name,
                        description: firestoreProduct.description || null,
                        price: firestoreProduct.price,
                        cost: firestoreProduct.cost || null,
                        sku: firestoreProduct.sku || null,
                        barcode: firestoreProduct.barcode || null,
                        type: firestoreProduct.type,
                        isActive: firestoreProduct.isActive,
                        stockQuantity: firestoreProduct.stockQuantity,
                        minStockLevel: firestoreProduct.minStockLevel,
                        trackInventory: firestoreProduct.trackInventory,
                        preparationTime: firestoreProduct.preparationTime || null,
                        calories: firestoreProduct.calories || null,
                        allergens: firestoreProduct.allergens || null,
                        categoryId: firestoreProduct.categoryId,
                        createdAt: firestoreProduct.createdAt,
                        updatedAt: firestoreProduct.updatedAt,
                    },
                });
                this.stats.products++;
                console.log(`  ✅ Rolled back product: ${firestoreProduct.name}`);
            }
            catch (error) {
                const errorMsg = `Failed to rollback product ${firestoreProduct.name}: ${error.message}`;
                console.error(`  ❌ ${errorMsg}`);
                this.stats.errors.push(errorMsg);
            }
        }
        console.log(`✅ Products rollback completed: ${this.stats.products} products rolled back\n`);
    }
    async rollbackCustomers() {
        console.log("👤 Rolling back Customers...");
        const firestoreCustomers = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.CUSTOMERS, { limit: 1000 });
        for (const firestoreCustomer of firestoreCustomers) {
            try {
                await prisma.customer.create({
                    data: {
                        id: firestoreCustomer.id,
                        firstName: firestoreCustomer.firstName || null,
                        lastName: firestoreCustomer.lastName || null,
                        email: firestoreCustomer.email || null,
                        phone: firestoreCustomer.phone || null,
                        dateOfBirth: firestoreCustomer.dateOfBirth || null,
                        isActive: firestoreCustomer.isActive,
                        createdAt: firestoreCustomer.createdAt,
                        updatedAt: firestoreCustomer.updatedAt,
                    },
                });
                this.stats.customers++;
                const customerName = `${firestoreCustomer.firstName || ""} ${firestoreCustomer.lastName || ""}`.trim() ||
                    firestoreCustomer.email ||
                    firestoreCustomer.id;
                console.log(`  ✅ Rolled back customer: ${customerName}`);
            }
            catch (error) {
                const errorMsg = `Failed to rollback customer ${firestoreCustomer.id}: ${error.message}`;
                console.error(`  ❌ ${errorMsg}`);
                this.stats.errors.push(errorMsg);
            }
        }
        console.log(`✅ Customers rollback completed: ${this.stats.customers} customers rolled back\n`);
    }
    async rollbackOrders() {
        console.log("🛒 Rolling back Orders with OrderItems and Payments...");
        const firestoreOrders = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.ORDERS, { limit: 1000 });
        for (const firestoreOrder of firestoreOrders) {
            try {
                // Create the order first
                const order = await prisma.order.create({
                    data: {
                        id: firestoreOrder.id,
                        orderNumber: firestoreOrder.orderNumber,
                        status: firestoreOrder.status,
                        subtotal: firestoreOrder.subtotal,
                        taxAmount: firestoreOrder.taxAmount,
                        discountAmount: firestoreOrder.discountAmount,
                        totalAmount: firestoreOrder.totalAmount,
                        customerId: firestoreOrder.customerId || null,
                        userId: firestoreOrder.userId,
                        orderDate: firestoreOrder.orderDate,
                        completedAt: firestoreOrder.completedAt || null,
                        createdAt: firestoreOrder.createdAt,
                        updatedAt: firestoreOrder.updatedAt,
                    },
                });
                // Create order items
                if (firestoreOrder.orderItems && firestoreOrder.orderItems.length > 0) {
                    for (const orderItem of firestoreOrder.orderItems) {
                        await prisma.orderItem.create({
                            data: {
                                id: orderItem.id,
                                orderId: order.id,
                                productId: orderItem.productId,
                                quantity: orderItem.quantity,
                                unitPrice: orderItem.unitPrice,
                                totalPrice: orderItem.totalPrice,
                                notes: orderItem.notes || null,
                                createdAt: orderItem.createdAt,
                                updatedAt: orderItem.updatedAt,
                            },
                        });
                        this.stats.orderItems++;
                    }
                }
                // Create payments
                if (firestoreOrder.payments && firestoreOrder.payments.length > 0) {
                    for (const payment of firestoreOrder.payments) {
                        await prisma.payment.create({
                            data: {
                                id: payment.id,
                                orderId: order.id,
                                amount: payment.amount,
                                method: payment.method,
                                status: payment.status,
                                reference: payment.reference || null,
                                notes: payment.notes || null,
                                createdAt: payment.createdAt,
                                updatedAt: payment.updatedAt,
                            },
                        });
                        this.stats.payments++;
                    }
                }
                this.stats.orders++;
                console.log(`  ✅ Rolled back order: ${firestoreOrder.orderNumber} (${firestoreOrder.orderItems?.length || 0} items, ${firestoreOrder.payments?.length || 0} payments)`);
            }
            catch (error) {
                const errorMsg = `Failed to rollback order ${firestoreOrder.orderNumber}: ${error.message}`;
                console.error(`  ❌ ${errorMsg}`);
                this.stats.errors.push(errorMsg);
            }
        }
        console.log(`✅ Orders rollback completed: ${this.stats.orders} orders rolled back\n`);
    }
    async validateRollback() {
        console.log("🔍 Validating rollback...");
        try {
            // Count documents in SQLite
            const sqliteUsers = await prisma.user.count();
            const sqliteCategories = await prisma.category.count();
            const sqliteProducts = await prisma.product.count();
            const sqliteCustomers = await prisma.customer.count();
            const sqliteOrders = await prisma.order.count();
            // Count documents in Firestore
            const firestoreUsers = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.USERS, { limit: 1000 });
            const firestoreCategories = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.CATEGORIES, { limit: 1000 });
            const firestoreProducts = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.PRODUCTS, { limit: 1000 });
            const firestoreCustomers = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.CUSTOMERS, { limit: 1000 });
            const firestoreOrders = await firestoreService_1.firestoreService.findMany(firestore_1.COLLECTIONS.ORDERS, { limit: 1000 });
            console.log("📊 Rollback Validation Results:");
            console.log(`  Users: Firestore(${firestoreUsers.length}) → SQLite(${sqliteUsers}) ${firestoreUsers.length === sqliteUsers ? "✅" : "❌"}`);
            console.log(`  Categories: Firestore(${firestoreCategories.length}) → SQLite(${sqliteCategories}) ${firestoreCategories.length === sqliteCategories ? "✅" : "❌"}`);
            console.log(`  Products: Firestore(${firestoreProducts.length}) → SQLite(${sqliteProducts}) ${firestoreProducts.length === sqliteProducts ? "✅" : "❌"}`);
            console.log(`  Customers: Firestore(${firestoreCustomers.length}) → SQLite(${sqliteCustomers}) ${firestoreCustomers.length === sqliteCustomers ? "✅" : "❌"}`);
            console.log(`  Orders: Firestore(${firestoreOrders.length}) → SQLite(${sqliteOrders}) ${firestoreOrders.length === sqliteOrders ? "✅" : "❌"}`);
            const allValid = firestoreUsers.length === sqliteUsers &&
                firestoreCategories.length === sqliteCategories &&
                firestoreProducts.length === sqliteProducts &&
                firestoreCustomers.length === sqliteCustomers &&
                firestoreOrders.length === sqliteOrders;
            if (allValid) {
                console.log("✅ Rollback validation passed!\n");
            }
            else {
                console.log("❌ Rollback validation failed - data counts do not match!\n");
                this.stats.errors.push("Rollback validation failed - data counts do not match");
            }
        }
        catch (error) {
            console.error("❌ Validation failed:", error);
            this.stats.errors.push(`Validation failed: ${error.message}`);
        }
    }
    printSummary() {
        console.log("📋 Rollback Summary:");
        console.log("=".repeat(50));
        console.log(`Users rolled back: ${this.stats.users}`);
        console.log(`Categories rolled back: ${this.stats.categories}`);
        console.log(`Products rolled back: ${this.stats.products}`);
        console.log(`Customers rolled back: ${this.stats.customers}`);
        console.log(`Orders rolled back: ${this.stats.orders}`);
        console.log(`Order Items rolled back: ${this.stats.orderItems}`);
        console.log(`Payments rolled back: ${this.stats.payments}`);
        console.log(`Errors encountered: ${this.stats.errors.length}`);
        if (this.stats.errors.length > 0) {
            console.log("\n❌ Errors:");
            this.stats.errors.forEach((error, index) => {
                console.log(`  ${index + 1}. ${error}`);
            });
        }
        console.log("=".repeat(50));
        if (this.stats.errors.length === 0) {
            console.log("🎉 Rollback completed successfully!");
        }
        else {
            console.log("⚠️ Rollback completed with errors. Please review the error list above.");
        }
    }
}
exports.DataRollback = DataRollback;
// Main execution
async function main() {
    const rollback = new DataRollback();
    try {
        await rollback.rollback();
    }
    catch (error) {
        console.error("💥 Rollback script failed:", error);
        process.exit(1);
    }
    finally {
        await prisma.$disconnect();
        console.log("\n👋 Rollback script finished.");
    }
}
// Run the rollback if this script is executed directly
if (require.main === module) {
    main().catch(console.error);
}
