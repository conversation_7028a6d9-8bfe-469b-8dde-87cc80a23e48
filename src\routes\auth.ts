import { Router } from 'express';
import { AuthController } from '../controllers/authController';
import { validate } from '../middleware/validation';
import { authenticate } from '../middleware/auth';
import { loginSchema, registerSchema, changePasswordSchema } from '../schemas/auth';

const router = Router();

// Public routes
router.post('/register', validate(registerSchema), AuthController.register);
router.post('/login', validate(loginSchema), AuthController.login);

// Protected routes
router.use(authenticate); // All routes below require authentication

router.post('/change-password', validate(changePasswordSchema), AuthController.changePassword);
router.post('/refresh-token', AuthController.refreshToken);
router.get('/profile', AuthController.getProfile);

export default router;
